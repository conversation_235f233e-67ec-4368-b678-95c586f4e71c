import 'package:country_code_picker/country_code_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:water_meter/utils/pok.dart';
import '../../views/widgets/containers/customTextField.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../../changeNotifiers/NudronChangeNotifiers.dart';
import '../../config.dart';
import '../../theme/theme2.dart';
import '../../utils/alert_message.dart';
import '../../utils/new_loader.dart';
import '../../view_model/loginPostRequests.dart';
import '../widgets/containers/customButton.dart';
import '../widgets/drawers/profile/country_code_picker2.dart';
import 'EnterTwoFacCodeSignUp.dart';

class RegisterPage extends StatefulWidget {
  const RegisterPage({super.key});

  @override
  State<RegisterPage> createState() => _RegisterPageState();
}

class _RegisterPageState extends State<RegisterPage> {
  TextEditingController nameController = TextEditingController();
  TextEditingController emailController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  TextEditingController activationCodeController = TextEditingController();
  CountryCode? selectedCountryCode = CountryCode(code: "IN");
  bool verifyPhoneAndEmail = false;
  bool isLargerTextField = ConfigurationCustom.isLargerTextField;

  void clearAllFields() {
    nameController.clear();
    emailController.clear();
    _phoneController.clear();
    activationCodeController.clear();
  }

  bool checkAllTextFields() {
    if (nameController.text.isEmpty ||
        emailController.text.isEmpty ||
        _phoneController.text.isEmpty ||
        activationCodeController.text.isEmpty) {
      CustomAlert.showCustomScaffoldMessenger(
          context, "Please fill all the fields", AlertType.warning);
      return false;
    }
    if (RegExp(r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9-]+\.[a-zA-Z]+") // TODO: Update in Mobile as well
            .hasMatch(emailController.text) ==
        false) {
      CustomAlert.showCustomScaffoldMessenger(
          context, "Please enter a valid email", AlertType.warning);
      return false;
    }
    if (selectedCountryCode == null || selectedCountryCode!.code == null) {
      CustomAlert.showCustomScaffoldMessenger(
          context, "Please select a country code", AlertType.warning);
      return false;
    }
    return true;
  }

  getPhoneNumberFull() {
    return (selectedCountryCode?.dialCode ?? '') + (_phoneController.text);
  }

  @override
  Widget build(BuildContext context) {
    DateTime today = DateTime.now();
    DateTime comparisonDate = DateTime(2025, 6, 18);

    if (today.isAfter(comparisonDate)) {
      return Stack(
        children: [
          Visibility(
            visible: !verifyPhoneAndEmail,
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: 80.w,
              ),
              child: Column(
                children: [
                  Padding(
                      padding: EdgeInsets.only(
                          left: 0.3.sw, right: 0.3.sw, bottom: 22.h),
                      child: CustomTextField(
                        key: UniqueKey(),
                        controller: nameController,
                        iconPath: 'assets/icons/profile2.svg',
                        hintText: 'Enter Full Name',
                        keyboardType: TextInputType.name,
                      )),
                  Padding(
                      padding: EdgeInsets.only(
                          left: 0.3.sw, right: 0.3.sw, bottom: 22.h),
                      child: CustomTextField(
                        controller: emailController,
                        key: UniqueKey(),
                        iconPath: 'assets/icons/mail.svg',
                        hintText: 'Enter Email',
                        keyboardType: TextInputType.emailAddress,
                      )),
                  Padding(
                      padding: EdgeInsets.only(
                          left: 0.3.sw, right: 0.3.sw, bottom: 22.h),
                      child: CustomTextField(
                        controller: _phoneController,
                        key: UniqueKey(),
                        hintText: 'Enter Phone number',
                        keyboardType: TextInputType.phone,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly,
                        ],
                        prefixIcon: Container(
                          // width: 60.w,
                          padding: EdgeInsets.only(left: 16.w - 8),
                          decoration: const BoxDecoration(
                              borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(10),
                                bottomLeft: Radius.circular(10),
                              ),
                              color: Colors.transparent),
                          // width: 42.w,
                          // height: 51.h,
                          child: CountryCodePicker2(
                            dropDownColor: Provider
                                .of<ThemeNotifier>(context)
                                .currentTheme
                                .textFieldBGProfile,
                            height: 30.78.h,
                            decoration: const BoxDecoration(
                              color: Colors.transparent,
                              borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(10),
                                bottomLeft: Radius.circular(10),
                              ),
                            ),
                            onChanged: (CountryCode countryCode) {
                              selectedCountryCode = countryCode;
                            },
                            getPhoneNumberWithoutCountryCode:
                                (String phoneNumber) {
                              _phoneController.text = phoneNumber;
                            },
                            // isEditable: false,
                            initialSelection: null,
                          ),
                        ),
                      )),
                  Padding(
                      padding: EdgeInsets.only(left: 0.3.sw, right: 0.3.sw),
                      child: CustomTextField(
                        controller: activationCodeController,
                        key: UniqueKey(),

                        // iconPath: 'assets/icons/mail.svg',
                        hintText: 'Enter Activation Code',
                        prefixIcon: Padding(
                            padding: EdgeInsets.symmetric(horizontal: 16.w),
                            child: Icon(
                              Icons.key,
                              color: Provider
                                  .of<ThemeNotifier>(context)
                                  .currentTheme
                                  .textfieldHintColor,
                            )),
                        // suffixIcon: GestureDetector(
                        //     //clipboard paste
                        //     onTap: () {
                        //       FlutterClipboard.paste().then((value) {
                        //         String code = value.toString().split("=").last;
                        //         activationCodeController.text = code;
                        //       });
                        //     },
                        //     child: Icon(
                        //       Icons.content_paste_go_outlined,
                        //       color: Provider.of<ThemeNotifier>(context)
                        //           .currentTheme
                        //           .textfieldHintColor,
                        //     )),
                      )),
                  SizedBox(height: 40.h),
                  Center(
                    child: CustomButton(
                      text: "REGISTER",
                      // fontSize: 14,
                      // fontSize: 16,
                      // height: 58.h,
                      onPressed: () async {
                        if (checkAllTextFields()) {
                          LoaderUtility.showLoader(
                              context,
                              LoginPostRequests.signUp(
                                  activationCodeController.text,
                                  nameController.text,
                                  emailController.text,
                                  getPhoneNumberFull()))
                              .then((value) {
                            CustomAlert.showCustomScaffoldMessenger(
                                context,
                                "Verification code sent to your email address and phone number",
                                AlertType.info);
                            setState(() {
                              verifyPhoneAndEmail = true;
                            });
                          }).catchError((e) {
                            CustomAlert.showCustomScaffoldMessenger(
                                context, e.toString(), AlertType.error);
                            return;
                          });
                        }
                      },
                    ),
                  ),
                  // SizedBox(height: 20.h),
                  // Text(
                  //     "If you are a customer of Nudron's smart meters and want access, please contact <NAME_EMAIL>",
                  //     textAlign: TextAlign.center,
                  //     style: GoogleFonts.roboto(
                  //         fontSize: 15.minSp,
                  //
                  //         // fontWeight: FontWeight.w500,
                  //         color: Provider.of<ThemeNotifier>(context)
                  //             .currentTheme
                  //             .basicAdvanceTextColor)),
                  SizedBox(height: 40.h),
                ],
              ),
            ),
          ),
          verifyPhoneAndEmail
              ? EnterTwoFacCodeSignUp(
            changePage: () {
              setState(() {
                clearAllFields();
                verifyPhoneAndEmail = false;
              });
              NudronRandomStuff.isSignIn.value = true;
            },
            key: UniqueKey(),
            activationCode: activationCodeController.text,
          )
              : Container(),
        ],
      );
    } else {
      return Stack(
        children: [
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: 80.w,
            ),
            child: Column(
              children: [
                SizedBox(height: 20.h),
                Center(
                  child: Text(
                      "If you are a customer of Nudron's smart meters and want access, please contact <NAME_EMAIL>",
                      textAlign: TextAlign.center,
                      style: GoogleFonts.roboto(
                          fontSize: ThemeNotifier.large.minSp,
                          // fontWeight: FontWeight.w500,
                          color: Provider.of<ThemeNotifier>(context)
                              .currentTheme
                              .basicAdvanceTextColor)),
                ),
                SizedBox(height: 40.h),
              ],
            ),
          ),
        ],
      );
    }

  }
}
