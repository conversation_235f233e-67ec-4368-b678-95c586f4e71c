  Activity android.app  Application android.app  RESULT_CANCELED android.app.Activity  	RESULT_OK android.app.Activity  application android.app.Activity  ActivityLifecycleCallbacks android.app.Application  "registerActivityLifecycleCallbacks android.app.Application  Intent android.content  SendIntentException android.content.IntentSender  Bundle 
android.os  Log android.util  e android.util.Log  OnFailureListener com.google.android.gms.tasks  OnSuccessListener com.google.android.gms.tasks  Task com.google.android.gms.tasks  <SAM-CONSTRUCTOR> .com.google.android.gms.tasks.OnFailureListener  <SAM-CONSTRUCTOR> .com.google.android.gms.tasks.OnSuccessListener  addOnFailureListener !com.google.android.gms.tasks.Task  addOnSuccessListener !com.google.android.gms.tasks.Task  
AppUpdateInfo &com.google.android.play.core.appupdate  AppUpdateManager &com.google.android.play.core.appupdate  AppUpdateManagerFactory &com.google.android.play.core.appupdate  AppUpdateOptions &com.google.android.play.core.appupdate  availableVersionCode 4com.google.android.play.core.appupdate.AppUpdateInfo  clientVersionStalenessDays 4com.google.android.play.core.appupdate.AppUpdateInfo  getFailedUpdatePreconditions 4com.google.android.play.core.appupdate.AppUpdateInfo  
installStatus 4com.google.android.play.core.appupdate.AppUpdateInfo  isUpdateTypeAllowed 4com.google.android.play.core.appupdate.AppUpdateInfo  packageName 4com.google.android.play.core.appupdate.AppUpdateInfo  updateAvailability 4com.google.android.play.core.appupdate.AppUpdateInfo  updatePriority 4com.google.android.play.core.appupdate.AppUpdateInfo  
appUpdateInfo 7com.google.android.play.core.appupdate.AppUpdateManager  completeUpdate 7com.google.android.play.core.appupdate.AppUpdateManager  registerListener 7com.google.android.play.core.appupdate.AppUpdateManager  startUpdateFlowForResult 7com.google.android.play.core.appupdate.AppUpdateManager  unregisterListener 7com.google.android.play.core.appupdate.AppUpdateManager  create >com.google.android.play.core.appupdate.AppUpdateManagerFactory  defaultOptions 7com.google.android.play.core.appupdate.AppUpdateOptions  InstallState $com.google.android.play.core.install  InstallStateUpdatedListener $com.google.android.play.core.install  installErrorCode 1com.google.android.play.core.install.InstallState  
installStatus 1com.google.android.play.core.install.InstallState  <SAM-CONSTRUCTOR> @com.google.android.play.core.install.InstallStateUpdatedListener  Activity *com.google.android.play.core.install.model  
ActivityAware *com.google.android.play.core.install.model  ActivityPluginBinding *com.google.android.play.core.install.model  ActivityProvider *com.google.android.play.core.install.model  ActivityResult *com.google.android.play.core.install.model  Any *com.google.android.play.core.install.model  
AppUpdateInfo *com.google.android.play.core.install.model  AppUpdateManager *com.google.android.play.core.install.model  AppUpdateManagerFactory *com.google.android.play.core.install.model  AppUpdateOptions *com.google.android.play.core.install.model  
AppUpdateType *com.google.android.play.core.install.model  Application *com.google.android.play.core.install.model  Boolean *com.google.android.play.core.install.model  Bundle *com.google.android.play.core.install.model  EventChannel *com.google.android.play.core.install.model  
FlutterPlugin *com.google.android.play.core.install.model  InstallErrorCode *com.google.android.play.core.install.model  InstallStateUpdatedListener *com.google.android.play.core.install.model  
InstallStatus *com.google.android.play.core.install.model  Int *com.google.android.play.core.install.model  Intent *com.google.android.play.core.install.model  Log *com.google.android.play.core.install.model  
MethodCall *com.google.android.play.core.install.model  MethodCallHandler *com.google.android.play.core.install.model  
MethodChannel *com.google.android.play.core.install.model  PluginRegistry *com.google.android.play.core.install.model  REQUEST_CODE_START_UPDATE *com.google.android.play.core.install.model  RESULT_CANCELED *com.google.android.play.core.install.model  	RESULT_OK *com.google.android.play.core.install.model  Result *com.google.android.play.core.install.model  SendIntentException *com.google.android.play.core.install.model  Unit *com.google.android.play.core.install.model  UpdateAvailability *com.google.android.play.core.install.model  map *com.google.android.play.core.install.model  mapOf *com.google.android.play.core.install.model  requireNotNull *com.google.android.play.core.install.model  to *com.google.android.play.core.install.model  toList *com.google.android.play.core.install.model  RESULT_IN_APP_UPDATE_FAILED 9com.google.android.play.core.install.model.ActivityResult  FLEXIBLE 8com.google.android.play.core.install.model.AppUpdateType  	IMMEDIATE 8com.google.android.play.core.install.model.AppUpdateType  ActivityLifecycleCallbacks 6com.google.android.play.core.install.model.Application  	EventSink 7com.google.android.play.core.install.model.EventChannel  
StreamHandler 7com.google.android.play.core.install.model.EventChannel  FlutterPluginBinding 8com.google.android.play.core.install.model.FlutterPlugin  NO_ERROR ;com.google.android.play.core.install.model.InstallErrorCode  
DOWNLOADED 8com.google.android.play.core.install.model.InstallStatus  ActivityResultListener 9com.google.android.play.core.install.model.PluginRegistry  &DEVELOPER_TRIGGERED_UPDATE_IN_PROGRESS =com.google.android.play.core.install.model.UpdateAvailability  Activity de.ffuf.in_app_update  
ActivityAware de.ffuf.in_app_update  ActivityPluginBinding de.ffuf.in_app_update  ActivityProvider de.ffuf.in_app_update  ActivityResult de.ffuf.in_app_update  Any de.ffuf.in_app_update  
AppUpdateInfo de.ffuf.in_app_update  AppUpdateManager de.ffuf.in_app_update  AppUpdateManagerFactory de.ffuf.in_app_update  AppUpdateOptions de.ffuf.in_app_update  
AppUpdateType de.ffuf.in_app_update  Application de.ffuf.in_app_update  Boolean de.ffuf.in_app_update  Bundle de.ffuf.in_app_update  EventChannel de.ffuf.in_app_update  
FlutterPlugin de.ffuf.in_app_update  InAppUpdatePlugin de.ffuf.in_app_update  InstallErrorCode de.ffuf.in_app_update  InstallStateUpdatedListener de.ffuf.in_app_update  
InstallStatus de.ffuf.in_app_update  Int de.ffuf.in_app_update  Intent de.ffuf.in_app_update  Log de.ffuf.in_app_update  
MethodCall de.ffuf.in_app_update  MethodCallHandler de.ffuf.in_app_update  
MethodChannel de.ffuf.in_app_update  PluginRegistry de.ffuf.in_app_update  REQUEST_CODE_START_UPDATE de.ffuf.in_app_update  RESULT_CANCELED de.ffuf.in_app_update  	RESULT_OK de.ffuf.in_app_update  Result de.ffuf.in_app_update  SendIntentException de.ffuf.in_app_update  Unit de.ffuf.in_app_update  UpdateAvailability de.ffuf.in_app_update  map de.ffuf.in_app_update  mapOf de.ffuf.in_app_update  requireNotNull de.ffuf.in_app_update  to de.ffuf.in_app_update  toList de.ffuf.in_app_update  activity &de.ffuf.in_app_update.ActivityProvider  addActivityResultListener &de.ffuf.in_app_update.ActivityProvider  ActivityLifecycleCallbacks !de.ffuf.in_app_update.Application  	EventSink "de.ffuf.in_app_update.EventChannel  
StreamHandler "de.ffuf.in_app_update.EventChannel  FlutterPluginBinding #de.ffuf.in_app_update.FlutterPlugin  Activity 'de.ffuf.in_app_update.InAppUpdatePlugin  ActivityPluginBinding 'de.ffuf.in_app_update.InAppUpdatePlugin  ActivityProvider 'de.ffuf.in_app_update.InAppUpdatePlugin  ActivityResult 'de.ffuf.in_app_update.InAppUpdatePlugin  Any 'de.ffuf.in_app_update.InAppUpdatePlugin  
AppUpdateInfo 'de.ffuf.in_app_update.InAppUpdatePlugin  AppUpdateManager 'de.ffuf.in_app_update.InAppUpdatePlugin  AppUpdateManagerFactory 'de.ffuf.in_app_update.InAppUpdatePlugin  AppUpdateOptions 'de.ffuf.in_app_update.InAppUpdatePlugin  
AppUpdateType 'de.ffuf.in_app_update.InAppUpdatePlugin  Boolean 'de.ffuf.in_app_update.InAppUpdatePlugin  Bundle 'de.ffuf.in_app_update.InAppUpdatePlugin  EventChannel 'de.ffuf.in_app_update.InAppUpdatePlugin  
FlutterPlugin 'de.ffuf.in_app_update.InAppUpdatePlugin  InstallErrorCode 'de.ffuf.in_app_update.InAppUpdatePlugin  InstallStateUpdatedListener 'de.ffuf.in_app_update.InAppUpdatePlugin  
InstallStatus 'de.ffuf.in_app_update.InAppUpdatePlugin  Int 'de.ffuf.in_app_update.InAppUpdatePlugin  Intent 'de.ffuf.in_app_update.InAppUpdatePlugin  Log 'de.ffuf.in_app_update.InAppUpdatePlugin  
MethodCall 'de.ffuf.in_app_update.InAppUpdatePlugin  
MethodChannel 'de.ffuf.in_app_update.InAppUpdatePlugin  PluginRegistry 'de.ffuf.in_app_update.InAppUpdatePlugin  REQUEST_CODE_START_UPDATE 'de.ffuf.in_app_update.InAppUpdatePlugin  RESULT_CANCELED 'de.ffuf.in_app_update.InAppUpdatePlugin  	RESULT_OK 'de.ffuf.in_app_update.InAppUpdatePlugin  Result 'de.ffuf.in_app_update.InAppUpdatePlugin  SendIntentException 'de.ffuf.in_app_update.InAppUpdatePlugin  Unit 'de.ffuf.in_app_update.InAppUpdatePlugin  UpdateAvailability 'de.ffuf.in_app_update.InAppUpdatePlugin  activityProvider 'de.ffuf.in_app_update.InAppUpdatePlugin  addState 'de.ffuf.in_app_update.InAppUpdatePlugin  
appUpdateInfo 'de.ffuf.in_app_update.InAppUpdatePlugin  appUpdateManager 'de.ffuf.in_app_update.InAppUpdatePlugin  
appUpdateType 'de.ffuf.in_app_update.InAppUpdatePlugin  channel 'de.ffuf.in_app_update.InAppUpdatePlugin  
checkAppState 'de.ffuf.in_app_update.InAppUpdatePlugin  checkForUpdate 'de.ffuf.in_app_update.InAppUpdatePlugin  completeFlexibleUpdate 'de.ffuf.in_app_update.InAppUpdatePlugin  event 'de.ffuf.in_app_update.InAppUpdatePlugin  installStateSink 'de.ffuf.in_app_update.InAppUpdatePlugin  installStateUpdatedListener 'de.ffuf.in_app_update.InAppUpdatePlugin  map 'de.ffuf.in_app_update.InAppUpdatePlugin  mapOf 'de.ffuf.in_app_update.InAppUpdatePlugin  performImmediateUpdate 'de.ffuf.in_app_update.InAppUpdatePlugin  requireNotNull 'de.ffuf.in_app_update.InAppUpdatePlugin  startFlexibleUpdate 'de.ffuf.in_app_update.InAppUpdatePlugin  to 'de.ffuf.in_app_update.InAppUpdatePlugin  toList 'de.ffuf.in_app_update.InAppUpdatePlugin  updateResult 'de.ffuf.in_app_update.InAppUpdatePlugin  ActivityResult 1de.ffuf.in_app_update.InAppUpdatePlugin.Companion  AppUpdateManagerFactory 1de.ffuf.in_app_update.InAppUpdatePlugin.Companion  AppUpdateOptions 1de.ffuf.in_app_update.InAppUpdatePlugin.Companion  
AppUpdateType 1de.ffuf.in_app_update.InAppUpdatePlugin.Companion  EventChannel 1de.ffuf.in_app_update.InAppUpdatePlugin.Companion  InstallErrorCode 1de.ffuf.in_app_update.InAppUpdatePlugin.Companion  InstallStateUpdatedListener 1de.ffuf.in_app_update.InAppUpdatePlugin.Companion  
InstallStatus 1de.ffuf.in_app_update.InAppUpdatePlugin.Companion  Log 1de.ffuf.in_app_update.InAppUpdatePlugin.Companion  
MethodChannel 1de.ffuf.in_app_update.InAppUpdatePlugin.Companion  REQUEST_CODE_START_UPDATE 1de.ffuf.in_app_update.InAppUpdatePlugin.Companion  RESULT_CANCELED 1de.ffuf.in_app_update.InAppUpdatePlugin.Companion  	RESULT_OK 1de.ffuf.in_app_update.InAppUpdatePlugin.Companion  UpdateAvailability 1de.ffuf.in_app_update.InAppUpdatePlugin.Companion  map 1de.ffuf.in_app_update.InAppUpdatePlugin.Companion  mapOf 1de.ffuf.in_app_update.InAppUpdatePlugin.Companion  requireNotNull 1de.ffuf.in_app_update.InAppUpdatePlugin.Companion  to 1de.ffuf.in_app_update.InAppUpdatePlugin.Companion  toList 1de.ffuf.in_app_update.InAppUpdatePlugin.Companion  	EventSink 4de.ffuf.in_app_update.InAppUpdatePlugin.EventChannel  FlutterPluginBinding 5de.ffuf.in_app_update.InAppUpdatePlugin.FlutterPlugin  ActivityResultListener 6de.ffuf.in_app_update.InAppUpdatePlugin.PluginRegistry  ActivityResultListener $de.ffuf.in_app_update.PluginRegistry  
FlutterPlugin #io.flutter.embedding.engine.plugins  FlutterPluginBinding 1io.flutter.embedding.engine.plugins.FlutterPlugin  binaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  
ActivityAware ,io.flutter.embedding.engine.plugins.activity  ActivityPluginBinding ,io.flutter.embedding.engine.plugins.activity  activity Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  addActivityResultListener Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  BinaryMessenger io.flutter.plugin.common  EventChannel io.flutter.plugin.common  
MethodCall io.flutter.plugin.common  
MethodChannel io.flutter.plugin.common  PluginRegistry io.flutter.plugin.common  	EventSink %io.flutter.plugin.common.EventChannel  
StreamHandler %io.flutter.plugin.common.EventChannel  setStreamHandler %io.flutter.plugin.common.EventChannel  success /io.flutter.plugin.common.EventChannel.EventSink  method #io.flutter.plugin.common.MethodCall  MethodCallHandler &io.flutter.plugin.common.MethodChannel  Result &io.flutter.plugin.common.MethodChannel  setMethodCallHandler &io.flutter.plugin.common.MethodChannel  error -io.flutter.plugin.common.MethodChannel.Result  notImplemented -io.flutter.plugin.common.MethodChannel.Result  success -io.flutter.plugin.common.MethodChannel.Result  ActivityResultListener 'io.flutter.plugin.common.PluginRegistry  	Exception 	java.lang  message java.lang.Exception  	Function0 kotlin  	Function1 kotlin  Nothing kotlin  Pair kotlin  Result kotlin  map kotlin  requireNotNull kotlin  to kotlin  toList kotlin  invoke kotlin.Function0  toInt 
kotlin.Int  toString 
kotlin.Int  to 
kotlin.String  message kotlin.Throwable  List kotlin.collections  Map kotlin.collections  map kotlin.collections  mapOf kotlin.collections  toList kotlin.collections  toList kotlin.collections.List  Sequence kotlin.sequences  map kotlin.sequences  toList kotlin.sequences  map kotlin.text  toList kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         