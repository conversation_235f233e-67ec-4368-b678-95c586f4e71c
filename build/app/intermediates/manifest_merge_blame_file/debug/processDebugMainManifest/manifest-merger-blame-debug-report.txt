1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.nudron.water_meter2"
4    android:versionCode="3"
5    android:versionName="1.0.6" >
6
7    <uses-sdk
8        android:minSdkVersion="29"
9        android:targetSdkVersion="34" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/AndroidManifest.xml:4:5-67
15-->/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/AndroidManifest.xml:4:22-64
16    <uses-permission android:name="android.permission.VIBRATE" />
16-->/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/AndroidManifest.xml:3:5-66
16-->/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/AndroidManifest.xml:3:22-63
17    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
17-->/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/AndroidManifest.xml:5:5-71
17-->/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/AndroidManifest.xml:5:22-69
18    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
18-->/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/AndroidManifest.xml:6:5-80
18-->/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/AndroidManifest.xml:6:22-78
19    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
19-->/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/AndroidManifest.xml:7:5-79
19-->/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/AndroidManifest.xml:7:22-77
20    <!--
21 Required to query activities that can process text, see:
22         https://developer.android.com/training/package-visibility?hl=en and
23         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
24
25         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
26    -->
27    <queries>
27-->/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/AndroidManifest.xml:57:5-66:15
28        <intent>
28-->/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/AndroidManifest.xml:58:9-61:18
29            <action android:name="android.intent.action.PROCESS_TEXT" />
29-->/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/AndroidManifest.xml:59:13-72
29-->/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/AndroidManifest.xml:59:21-70
30
31            <data android:mimeType="text/plain" />
31-->/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/AndroidManifest.xml:60:13-50
31-->/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/AndroidManifest.xml:60:19-48
32        </intent>
33        <intent>
33-->/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/AndroidManifest.xml:62:9-65:18
34            <action android:name="android.intent.action.VIEW" />
34-->/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/AndroidManifest.xml:63:13-65
34-->/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/AndroidManifest.xml:63:21-62
35
36            <data android:scheme="otpauth" />
36-->/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/AndroidManifest.xml:60:13-50
37        </intent>
38    </queries>
39
40    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- suppress DeprecatedClassUsageInspection -->
40-->[:connectivity_plus] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/connectivity_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-79
40-->[:connectivity_plus] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/connectivity_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:22-76
41    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
41-->[androidx.biometric:biometric:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1bcae24384bdfeff7289d35aaccaf47c/transformed/biometric-1.1.0/AndroidManifest.xml:27:5-74
41-->[androidx.biometric:biometric:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1bcae24384bdfeff7289d35aaccaf47c/transformed/biometric-1.1.0/AndroidManifest.xml:27:22-71
42
43    <permission
43-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/6589d333780c4589838d099a13cdb896/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
44        android:name="com.nudron.water_meter2.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
44-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/6589d333780c4589838d099a13cdb896/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
45        android:protectionLevel="signature" />
45-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/6589d333780c4589838d099a13cdb896/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
46
47    <uses-permission android:name="com.nudron.water_meter2.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
47-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/6589d333780c4589838d099a13cdb896/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
47-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/6589d333780c4589838d099a13cdb896/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
48
49    <application
50        android:name="android.app.Application"
51        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
51-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/6589d333780c4589838d099a13cdb896/transformed/core-1.13.1/AndroidManifest.xml:28:18-86
52        android:debuggable="true"
53        android:extractNativeLibs="false"
54        android:icon="@mipmap/ic_launcher"
55        android:label="Water Meter" >
56        <provider
57            android:name="androidx.core.content.FileProvider"
58            android:authorities="com.nudron.water_meter2.fileProvider"
59            android:exported="false"
60            android:grantUriPermissions="true" >
61            <meta-data
61-->[:open_file_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/open_file_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:16:13-18:53
62                android:name="android.support.FILE_PROVIDER_PATHS"
62-->[:open_file_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/open_file_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:17:17-67
63                android:resource="@xml/filepaths" />
63-->[:open_file_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/open_file_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:18:17-50
64        </provider>
65
66        <activity
67            android:name="io.flutter.embedding.android.FlutterFragmentActivity"
68            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
69            android:exported="true"
70            android:hardwareAccelerated="true"
71            android:launchMode="singleTop"
72            android:theme="@style/Theme.AppCompat.DayNight"
73            android:windowSoftInputMode="adjustResize" >
74
75            <!--
76                 Specifies an Android theme to apply to this Activity as soon as
77                 the Android process has started. This theme is visible to the user
78                 while the Flutter UI initializes. After that, this theme continues
79                 to determine the Window background behind the Flutter UI.
80            -->
81            <meta-data
82                android:name="io.flutter.embedding.android.NormalTheme"
83                android:resource="@style/NormalTheme" />
84
85            <intent-filter>
86                <action android:name="android.intent.action.MAIN" />
87
88                <category android:name="android.intent.category.LAUNCHER" />
89            </intent-filter>
90        </activity>
91        <!--
92             Don't delete the meta-data below.
93             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
94        -->
95        <meta-data
96            android:name="flutterEmbedding"
97            android:value="2" />
98
99        <activity
99-->[:url_launcher_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-11:74
100            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
100-->[:url_launcher_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-74
101            android:exported="false"
101-->[:url_launcher_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-37
102            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
102-->[:url_launcher_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-71
103
104        <provider
104-->[:open_file_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/open_file_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:9-19:20
105            android:name="com.crazecoder.openfile.FileProvider"
105-->[:open_file_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/open_file_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-64
106            android:authorities="com.nudron.water_meter2.fileProvider.com.crazecoder.openfile"
106-->[:open_file_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/open_file_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-88
107            android:exported="false"
107-->[:open_file_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/open_file_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:13-37
108            android:grantUriPermissions="true"
108-->[:open_file_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/open_file_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:13-47
109            android:requestLegacyExternalStorage="true" >
109-->[:open_file_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/open_file_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:13-56
110            <meta-data
110-->[:open_file_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/open_file_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:16:13-18:53
111                android:name="android.support.FILE_PROVIDER_PATHS"
111-->[:open_file_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/open_file_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:17:17-67
112                android:resource="@xml/filepaths" />
112-->[:open_file_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/open_file_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:18:17-50
113        </provider>
114
115        <uses-library
115-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/697d4a371d14a343f07c7af23d58e667/transformed/jetified-window-1.2.0/AndroidManifest.xml:23:9-25:40
116            android:name="androidx.window.extensions"
116-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/697d4a371d14a343f07c7af23d58e667/transformed/jetified-window-1.2.0/AndroidManifest.xml:24:13-54
117            android:required="false" />
117-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/697d4a371d14a343f07c7af23d58e667/transformed/jetified-window-1.2.0/AndroidManifest.xml:25:13-37
118        <uses-library
118-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/697d4a371d14a343f07c7af23d58e667/transformed/jetified-window-1.2.0/AndroidManifest.xml:26:9-28:40
119            android:name="androidx.window.sidecar"
119-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/697d4a371d14a343f07c7af23d58e667/transformed/jetified-window-1.2.0/AndroidManifest.xml:27:13-51
120            android:required="false" />
120-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/697d4a371d14a343f07c7af23d58e667/transformed/jetified-window-1.2.0/AndroidManifest.xml:28:13-37
121
122        <provider
122-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6b3eecdb85677d1cf7cfc939b614c019/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:24:9-32:20
123            android:name="androidx.startup.InitializationProvider"
123-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6b3eecdb85677d1cf7cfc939b614c019/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:25:13-67
124            android:authorities="com.nudron.water_meter2.androidx-startup"
124-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6b3eecdb85677d1cf7cfc939b614c019/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:26:13-68
125            android:exported="false" >
125-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6b3eecdb85677d1cf7cfc939b614c019/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:27:13-37
126            <meta-data
126-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6b3eecdb85677d1cf7cfc939b614c019/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:29:13-31:52
127                android:name="androidx.emoji2.text.EmojiCompatInitializer"
127-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6b3eecdb85677d1cf7cfc939b614c019/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:30:17-75
128                android:value="androidx.startup" />
128-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6b3eecdb85677d1cf7cfc939b614c019/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:31:17-49
129            <meta-data
129-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/3528b7108514549d665af8216f1a14d0/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
130                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
130-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/3528b7108514549d665af8216f1a14d0/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
131                android:value="androidx.startup" />
131-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/3528b7108514549d665af8216f1a14d0/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
132            <meta-data
132-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5aebf1ccadd4f90aa31b994548591d8/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
133                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
133-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5aebf1ccadd4f90aa31b994548591d8/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
134                android:value="androidx.startup" />
134-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5aebf1ccadd4f90aa31b994548591d8/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
135        </provider>
136
137        <activity
137-->[com.google.android.gms:play-services-auth:21.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b221ec0a1202bc0a23dfdf8c6907b5a5/transformed/jetified-play-services-auth-21.2.0/AndroidManifest.xml:23:9-27:75
138            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
138-->[com.google.android.gms:play-services-auth:21.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b221ec0a1202bc0a23dfdf8c6907b5a5/transformed/jetified-play-services-auth-21.2.0/AndroidManifest.xml:24:13-93
139            android:excludeFromRecents="true"
139-->[com.google.android.gms:play-services-auth:21.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b221ec0a1202bc0a23dfdf8c6907b5a5/transformed/jetified-play-services-auth-21.2.0/AndroidManifest.xml:25:13-46
140            android:exported="false"
140-->[com.google.android.gms:play-services-auth:21.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b221ec0a1202bc0a23dfdf8c6907b5a5/transformed/jetified-play-services-auth-21.2.0/AndroidManifest.xml:26:13-37
141            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
141-->[com.google.android.gms:play-services-auth:21.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b221ec0a1202bc0a23dfdf8c6907b5a5/transformed/jetified-play-services-auth-21.2.0/AndroidManifest.xml:27:13-72
142        <!--
143            Service handling Google Sign-In user revocation. For apps that do not integrate with
144            Google Sign-In, this service will never be started.
145        -->
146        <service
146-->[com.google.android.gms:play-services-auth:21.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b221ec0a1202bc0a23dfdf8c6907b5a5/transformed/jetified-play-services-auth-21.2.0/AndroidManifest.xml:33:9-37:51
147            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
147-->[com.google.android.gms:play-services-auth:21.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b221ec0a1202bc0a23dfdf8c6907b5a5/transformed/jetified-play-services-auth-21.2.0/AndroidManifest.xml:34:13-89
148            android:exported="true"
148-->[com.google.android.gms:play-services-auth:21.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b221ec0a1202bc0a23dfdf8c6907b5a5/transformed/jetified-play-services-auth-21.2.0/AndroidManifest.xml:35:13-36
149            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
149-->[com.google.android.gms:play-services-auth:21.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b221ec0a1202bc0a23dfdf8c6907b5a5/transformed/jetified-play-services-auth-21.2.0/AndroidManifest.xml:36:13-107
150            android:visibleToInstantApps="true" />
150-->[com.google.android.gms:play-services-auth:21.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b221ec0a1202bc0a23dfdf8c6907b5a5/transformed/jetified-play-services-auth-21.2.0/AndroidManifest.xml:37:13-48
151
152        <activity
152-->[com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/c9d9b59e0faf7f1ee31dcea41f6a6a66/transformed/jetified-play-services-base-18.3.0/AndroidManifest.xml:20:9-22:45
153            android:name="com.google.android.gms.common.api.GoogleApiActivity"
153-->[com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/c9d9b59e0faf7f1ee31dcea41f6a6a66/transformed/jetified-play-services-base-18.3.0/AndroidManifest.xml:20:19-85
154            android:exported="false"
154-->[com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/c9d9b59e0faf7f1ee31dcea41f6a6a66/transformed/jetified-play-services-base-18.3.0/AndroidManifest.xml:22:19-43
155            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
155-->[com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/c9d9b59e0faf7f1ee31dcea41f6a6a66/transformed/jetified-play-services-base-18.3.0/AndroidManifest.xml:21:19-78
156
157        <meta-data
157-->[com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/74c58a9fa9a78d6cda2a74380ccc34d2/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:21:9-23:69
158            android:name="com.google.android.gms.version"
158-->[com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/74c58a9fa9a78d6cda2a74380ccc34d2/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:22:13-58
159            android:value="@integer/google_play_services_version" />
159-->[com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/74c58a9fa9a78d6cda2a74380ccc34d2/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:23:13-66
160
161        <receiver
161-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5aebf1ccadd4f90aa31b994548591d8/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
162            android:name="androidx.profileinstaller.ProfileInstallReceiver"
162-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5aebf1ccadd4f90aa31b994548591d8/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
163            android:directBootAware="false"
163-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5aebf1ccadd4f90aa31b994548591d8/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
164            android:enabled="true"
164-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5aebf1ccadd4f90aa31b994548591d8/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
165            android:exported="true"
165-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5aebf1ccadd4f90aa31b994548591d8/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
166            android:permission="android.permission.DUMP" >
166-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5aebf1ccadd4f90aa31b994548591d8/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
167            <intent-filter>
167-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5aebf1ccadd4f90aa31b994548591d8/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
168                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
168-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5aebf1ccadd4f90aa31b994548591d8/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
168-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5aebf1ccadd4f90aa31b994548591d8/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
169            </intent-filter>
170            <intent-filter>
170-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5aebf1ccadd4f90aa31b994548591d8/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
171                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
171-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5aebf1ccadd4f90aa31b994548591d8/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
171-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5aebf1ccadd4f90aa31b994548591d8/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
172            </intent-filter>
173            <intent-filter>
173-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5aebf1ccadd4f90aa31b994548591d8/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
174                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
174-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5aebf1ccadd4f90aa31b994548591d8/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
174-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5aebf1ccadd4f90aa31b994548591d8/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
175            </intent-filter>
176            <intent-filter>
176-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5aebf1ccadd4f90aa31b994548591d8/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
177                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
177-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5aebf1ccadd4f90aa31b994548591d8/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
177-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5aebf1ccadd4f90aa31b994548591d8/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
178            </intent-filter>
179        </receiver> <!-- The activities will be merged into the manifest of the hosting app. -->
180        <activity
180-->[com.google.android.play:core-common:2.0.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/27da46d50e2d40e533249fccc0ec02b9/transformed/jetified-core-common-2.0.3/AndroidManifest.xml:14:9-18:65
181            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
181-->[com.google.android.play:core-common:2.0.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/27da46d50e2d40e533249fccc0ec02b9/transformed/jetified-core-common-2.0.3/AndroidManifest.xml:15:13-93
182            android:exported="false"
182-->[com.google.android.play:core-common:2.0.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/27da46d50e2d40e533249fccc0ec02b9/transformed/jetified-core-common-2.0.3/AndroidManifest.xml:16:13-37
183            android:stateNotNeeded="true"
183-->[com.google.android.play:core-common:2.0.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/27da46d50e2d40e533249fccc0ec02b9/transformed/jetified-core-common-2.0.3/AndroidManifest.xml:17:13-42
184            android:theme="@style/Theme.PlayCore.Transparent" />
184-->[com.google.android.play:core-common:2.0.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/27da46d50e2d40e533249fccc0ec02b9/transformed/jetified-core-common-2.0.3/AndroidManifest.xml:18:13-62
185    </application>
186
187</manifest>
