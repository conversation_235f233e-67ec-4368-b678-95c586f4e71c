{"logs": [{"outputFile": "com.nudron.water_metering.app-mergeDebugResources-47:/values-zh-rHK/values-zh-rHK.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/c9d9b59e0faf7f1ee31dcea41f6a6a66/transformed/jetified-play-services-base-18.3.0/res/values-zh-rHK/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,294,418,529,627,730,842,940,1029,1135,1232,1357,1468,1571,1675,1726,1779", "endColumns": "96,123,110,97,102,111,97,88,105,96,124,110,102,103,50,52,67", "endOffsets": "293,417,528,626,729,841,939,1028,1134,1231,1356,1467,1570,1674,1725,1778,1846"}, "to": {"startLines": "36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3327,3428,3556,3671,3773,3880,3996,4098,4299,4409,4510,4639,4754,4861,4969,5024,5081", "endColumns": "100,127,114,101,106,115,101,92,109,100,128,114,106,107,54,56,71", "endOffsets": "3423,3551,3666,3768,3875,3991,4093,4186,4404,4505,4634,4749,4856,4964,5019,5076,5148"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/6589d333780c4589838d099a13cdb896/transformed/core-1.13.1/res/values-zh-rHK/values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "29,30,31,32,33,34,35,75", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2666,2758,2857,2951,3045,3138,3231,7119", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "2753,2852,2946,3040,3133,3226,3322,7215"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/74c58a9fa9a78d6cda2a74380ccc34d2/transformed/jetified-play-services-basement-18.3.0/res/values-zh-rHK/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "103", "endOffsets": "302"}, "to": {"startLines": "44", "startColumns": "4", "startOffsets": "4191", "endColumns": "107", "endOffsets": "4294"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/1bcae24384bdfeff7289d35aaccaf47c/transformed/biometric-1.1.0/res/values-zh-rHK/values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,153,234,333,436,538,636,738,840,930,1038,1141", "endColumns": "97,80,98,102,101,97,101,101,89,107,102,95", "endOffsets": "148,229,328,431,533,631,733,835,925,1033,1136,1232"}, "to": {"startLines": "54,57,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5153,5400,5848,5947,6050,6152,6250,6352,6454,6544,6652,6755", "endColumns": "97,80,98,102,101,97,101,101,89,107,102,95", "endOffsets": "5246,5476,5942,6045,6147,6245,6347,6449,6539,6647,6750,6846"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/ff9de53921e81b72effb184a4ee977a5/transformed/browser-1.8.0/res/values-zh-rHK/values-zh-rHK.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,138,230,331", "endColumns": "82,91,100,92", "endOffsets": "133,225,326,419"}, "to": {"startLines": "56,59,60,61", "startColumns": "4,4,4,4", "startOffsets": "5317,5562,5654,5755", "endColumns": "82,91,100,92", "endOffsets": "5395,5649,5750,5843"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/c0dfc2e5d5f124c9ed5c4a17b0deea03/transformed/preference-1.2.1/res/values-zh-rHK/values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,171,252,322,441,609,688", "endColumns": "65,80,69,118,167,78,75", "endOffsets": "166,247,317,436,604,683,759"}, "to": {"startLines": "55,58,72,73,76,77,78", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5251,5481,6851,6921,7220,7388,7467", "endColumns": "65,80,69,118,167,78,75", "endOffsets": "5312,5557,6916,7035,7383,7462,7538"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/71d91ec26cc715ac194e9af65b3a48ac/transformed/appcompat-1.6.1/res/values-zh-rHK/values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1109,1205,1300,1394,1490,1582,1674,1766,1844,1940,2035,2130,2227,2323,2421,2572,2666", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1104,1200,1295,1389,1485,1577,1669,1761,1839,1935,2030,2125,2222,2318,2416,2567,2661,2740"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1109,1205,1300,1394,1490,1582,1674,1766,1844,1940,2035,2130,2227,2323,2421,2572,7040", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1104,1200,1295,1389,1485,1577,1669,1761,1839,1935,2030,2125,2222,2318,2416,2567,2661,7114"}}]}]}