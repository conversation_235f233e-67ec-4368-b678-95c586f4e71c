{"logs": [{"outputFile": "com.nudron.water_metering.app-mergeDebugResources-47:/values/values.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/ff9de53921e81b72effb184a4ee977a5/transformed/browser-1.8.0/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "116,117,118,119,260,261,465,468,469,470", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "4870,4928,4994,5057,14546,14617,28453,28638,28705,28784", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "4923,4989,5052,5114,14612,14684,28516,28700,28779,28848"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/74c58a9fa9a78d6cda2a74380ccc34d2/transformed/jetified-play-services-basement-18.3.0/res/values/values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "406,453", "startColumns": "4,4", "startOffsets": "23282,27046", "endColumns": "67,166", "endOffsets": "23345,27208"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/50ac106c41e657d661b010ef25e09e3e/transformed/coordinatorlayout-1.0.0/res/values/values.xml", "from": {"startLines": "2,102,3,13", "startColumns": "4,4,4,4", "startOffsets": "55,5935,116,724", "endLines": "2,104,12,101", "endColumns": "60,12,24,24", "endOffsets": "111,6075,719,5930"}, "to": {"startLines": "35,2194,2941,2947", "startColumns": "4,4,4,4", "startOffsets": "1213,143217,168661,168872", "endLines": "35,2196,2946,3030", "endColumns": "60,12,24,24", "endOffsets": "1269,143357,168867,173383"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/71d91ec26cc715ac194e9af65b3a48ac/transformed/appcompat-1.6.1/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "36,59,60,91,92,93,95,96,97,98,99,100,101,104,105,106,107,110,111,112,113,114,115,120,121,132,133,134,135,136,137,138,139,141,142,144,145,146,147,148,149,150,151,152,153,154,155,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,269,270,275,276,277,278,279,280,281,309,310,311,312,313,314,315,316,352,353,354,355,360,368,369,374,396,402,403,404,405,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,485,490,491,498,499,500,501,509,510,514,518,522,527,533,540,544,548,553,557,561,565,569,573,577,583,587,593,597,603,607,612,616,619,623,629,633,639,643,649,652,656,660,664,668,672,673,674,675,678,681,684,687,691,692,693,694,695,698,700,702,704,709,710,714,720,724,725,727,739,740,744,750,754,755,756,760,787,791,792,796,824,996,1022,1193,1219,1250,1258,1264,1280,1302,1307,1312,1322,1331,1340,1344,1351,1370,1377,1378,1387,1390,1393,1397,1401,1405,1408,1409,1414,1419,1429,1434,1441,1447,1448,1451,1455,1460,1462,1464,1467,1470,1472,1476,1479,1486,1489,1492,1496,1498,1502,1504,1506,1508,1512,1520,1528,1540,1546,1555,1558,1569,1572,1573,1578,1579,1608,1677,1747,1748,1758,1767,1919,1921,1925,1928,1931,1934,1937,1940,1943,1946,1950,1953,1956,1959,1963,1966,1970,1974,1975,1976,1977,1978,1979,1980,1981,1982,1983,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1996,1998,1999,2000,2001,2002,2003,2004,2005,2007,2008,2010,2011,2013,2015,2016,2018,2019,2020,2021,2022,2023,2025,2026,2027,2028,2029,2041,2043,2045,2047,2048,2049,2050,2051,2052,2053,2054,2055,2056,2057,2058,2059,2061,2062,2063,2064,2065,2066,2067,2069,2073,2085,2086,2087,2088,2089,2090,2094,2095,2096,2097,2099,2101,2103,2105,2107,2108,2109,2110,2112,2114,2116,2117,2118,2119,2120,2121,2122,2123,2124,2125,2126,2127,2130,2131,2132,2133,2135,2137,2138,2140,2141,2143,2145,2147,2148,2149,2150,2151,2152,2153,2154,2155,2156,2157,2158,2160,2161,2162,2163,2165,2166,2167,2168,2169,2171,2173,2175,2177,2178,2179,2180,2181,2182,2183,2184,2185,2186,2187,2188,2189,2190,2191,2197,2272,2275,2278,2281,2295,2312,2354,2357,2386,2413,2422,2486,2854,2875,2913,3051,3175,3199,3205,3234,3255,3379,3407,3413,3557,3583,3650,3721,3821,3841,3896,3908,3934", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1274,1940,1985,3032,3073,3128,3263,3327,3397,3458,3533,3609,3686,3924,4009,4091,4167,4344,4421,4499,4605,4711,4790,5119,5176,6036,6110,6185,6250,6316,6376,6437,6509,6626,6693,6818,6877,6936,6995,7054,7113,7167,7221,7274,7328,7382,7436,7691,7765,7844,7917,7991,8062,8134,8206,8279,8336,8394,8467,8541,8615,8690,8762,8835,8905,8976,9036,9097,9166,9235,9305,9379,9455,9519,9596,9672,9749,9814,9883,9960,10035,10104,10172,10249,10315,10376,10473,10538,10607,10706,10777,10836,10894,10951,11010,11074,11145,11217,11289,11361,11433,11500,11568,11636,11695,11758,11822,11912,12003,12063,12129,12196,12262,12332,12396,12449,12516,12577,12644,12757,12815,12878,12943,13008,13083,13156,13228,13272,13319,13365,13414,13475,13536,13597,13659,13723,13787,13851,13916,13979,14039,14100,14166,14225,14285,14347,14418,14478,15177,15263,15566,15656,15743,15831,15913,15996,16086,17905,17957,18015,18060,18126,18190,18247,18304,20481,20538,20586,20635,20890,21260,21307,21565,22736,23039,23103,23165,23225,23546,23620,23690,23768,23822,23892,23977,24025,24071,24132,24195,24261,24325,24396,24459,24524,24588,24649,24710,24762,24835,24909,24978,25053,25127,25201,25342,29984,30345,30423,30813,30901,30997,31087,31669,31758,32005,32286,32538,32823,33216,33693,33915,34137,34413,34640,34870,35100,35330,35560,35787,36206,36432,36857,37087,37515,37734,38017,38225,38356,38583,39009,39234,39661,39882,40307,40427,40703,41004,41328,41619,41933,42070,42201,42306,42548,42715,42919,43127,43398,43510,43622,43727,43844,44058,44204,44344,44430,44778,44866,45112,45530,45779,45861,45959,46616,46716,46968,47392,47647,47741,47830,48067,50091,50333,50435,50688,52844,63525,65041,75736,77264,79021,79647,80067,81328,82593,82849,83085,83632,84126,84731,84929,85509,86877,87252,87370,87908,88065,88261,88534,88790,88960,89101,89165,89530,89897,90573,90837,91175,91528,91622,91808,92114,92376,92501,92628,92867,93078,93197,93390,93567,94022,94203,94325,94584,94697,94884,94986,95093,95222,95497,96005,96501,97378,97672,98242,98391,99123,99295,99379,99715,99807,101873,107104,112475,112537,113115,113699,121646,121759,121988,122148,122300,122471,122637,122806,122973,123136,123379,123549,123722,123893,124167,124366,124571,124901,124985,125081,125177,125275,125375,125477,125579,125681,125783,125885,125985,126081,126193,126322,126445,126576,126707,126805,126919,127013,127153,127287,127383,127495,127595,127711,127807,127919,128019,128159,128295,128459,128589,128747,128897,129038,129182,129317,129429,129579,129707,129835,129971,130103,130233,130363,130475,131373,131519,131663,131801,131867,131957,132033,132137,132227,132329,132437,132545,132645,132725,132817,132915,133025,133077,133155,133261,133353,133457,133567,133689,133852,134419,134499,134599,134689,134799,134889,135130,135224,135330,135422,135522,135634,135748,135864,135980,136074,136188,136300,136402,136522,136644,136726,136830,136950,137076,137174,137268,137356,137468,137584,137706,137818,137993,138109,138195,138287,138399,138523,138590,138716,138784,138912,139056,139184,139253,139348,139463,139576,139675,139784,139895,140006,140107,140212,140312,140442,140533,140656,140750,140862,140948,141052,141148,141236,141354,141458,141562,141688,141776,141884,141984,142074,142184,142268,142370,142454,142508,142572,142678,142764,142874,142958,143362,145978,146096,146211,146291,146652,147238,148642,148720,150064,151425,151813,154656,164894,165633,167304,174117,178418,179169,179431,180278,180657,184935,185789,186018,190626,191636,193588,195988,200112,200856,202987,203327,204638", "endLines": "36,59,60,91,92,93,95,96,97,98,99,100,101,104,105,106,107,110,111,112,113,114,115,120,121,132,133,134,135,136,137,138,139,141,142,144,145,146,147,148,149,150,151,152,153,154,155,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,269,270,275,276,277,278,279,280,281,309,310,311,312,313,314,315,316,352,353,354,355,360,368,369,374,396,402,403,404,405,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,485,490,491,498,499,500,508,509,513,517,521,526,532,539,543,547,552,556,560,564,568,572,576,582,586,592,596,602,606,611,615,618,622,628,632,638,642,648,651,655,659,663,667,671,672,673,674,677,680,683,686,690,691,692,693,694,697,699,701,703,708,709,713,719,723,724,726,738,739,743,749,753,754,755,759,786,790,791,795,823,995,1021,1192,1218,1249,1257,1263,1279,1301,1306,1311,1321,1330,1339,1343,1350,1369,1376,1377,1386,1389,1392,1396,1400,1404,1407,1408,1413,1418,1428,1433,1440,1446,1447,1450,1454,1459,1461,1463,1466,1469,1471,1475,1478,1485,1488,1491,1495,1497,1501,1503,1505,1507,1511,1519,1527,1539,1545,1554,1557,1568,1571,1572,1577,1578,1583,1676,1746,1747,1757,1766,1767,1920,1924,1927,1930,1933,1936,1939,1942,1945,1949,1952,1955,1958,1962,1965,1969,1973,1974,1975,1976,1977,1978,1979,1980,1981,1982,1983,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1995,1997,1998,1999,2000,2001,2002,2003,2004,2006,2007,2009,2010,2012,2014,2015,2017,2018,2019,2020,2021,2022,2024,2025,2026,2027,2028,2029,2042,2044,2046,2047,2048,2049,2050,2051,2052,2053,2054,2055,2056,2057,2058,2060,2061,2062,2063,2064,2065,2066,2068,2072,2076,2085,2086,2087,2088,2089,2093,2094,2095,2096,2098,2100,2102,2104,2106,2107,2108,2109,2111,2113,2115,2116,2117,2118,2119,2120,2121,2122,2123,2124,2125,2126,2129,2130,2131,2132,2134,2136,2137,2139,2140,2142,2144,2146,2147,2148,2149,2150,2151,2152,2153,2154,2155,2156,2157,2159,2160,2161,2162,2164,2165,2166,2167,2168,2170,2172,2174,2176,2177,2178,2179,2180,2181,2182,2183,2184,2185,2186,2187,2188,2189,2190,2191,2271,2274,2277,2280,2294,2300,2321,2356,2385,2412,2421,2485,2848,2857,2902,2940,3068,3198,3204,3210,3254,3378,3398,3412,3416,3562,3617,3661,3786,3840,3895,3907,3933,3940", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "1324,1980,2029,3068,3123,3185,3322,3392,3453,3528,3604,3681,3759,4004,4086,4162,4238,4416,4494,4600,4706,4785,4865,5171,5229,6105,6180,6245,6311,6371,6432,6504,6577,6688,6756,6872,6931,6990,7049,7108,7162,7216,7269,7323,7377,7431,7485,7760,7839,7912,7986,8057,8129,8201,8274,8331,8389,8462,8536,8610,8685,8757,8830,8900,8971,9031,9092,9161,9230,9300,9374,9450,9514,9591,9667,9744,9809,9878,9955,10030,10099,10167,10244,10310,10371,10468,10533,10602,10701,10772,10831,10889,10946,11005,11069,11140,11212,11284,11356,11428,11495,11563,11631,11690,11753,11817,11907,11998,12058,12124,12191,12257,12327,12391,12444,12511,12572,12639,12752,12810,12873,12938,13003,13078,13151,13223,13267,13314,13360,13409,13470,13531,13592,13654,13718,13782,13846,13911,13974,14034,14095,14161,14220,14280,14342,14413,14473,14541,15258,15345,15651,15738,15826,15908,15991,16081,16172,17952,18010,18055,18121,18185,18242,18299,18353,20533,20581,20630,20681,20919,21302,21351,21606,22763,23098,23160,23220,23277,23615,23685,23763,23817,23887,23972,24020,24066,24127,24190,24256,24320,24391,24454,24519,24583,24644,24705,24757,24830,24904,24973,25048,25122,25196,25337,25407,30032,30418,30508,30896,30992,31082,31664,31753,32000,32281,32533,32818,33211,33688,33910,34132,34408,34635,34865,35095,35325,35555,35782,36201,36427,36852,37082,37510,37729,38012,38220,38351,38578,39004,39229,39656,39877,40302,40422,40698,40999,41323,41614,41928,42065,42196,42301,42543,42710,42914,43122,43393,43505,43617,43722,43839,44053,44199,44339,44425,44773,44861,45107,45525,45774,45856,45954,46611,46711,46963,47387,47642,47736,47825,48062,50086,50328,50430,50683,52839,63520,65036,75731,77259,79016,79642,80062,81323,82588,82844,83080,83627,84121,84726,84924,85504,86872,87247,87365,87903,88060,88256,88529,88785,88955,89096,89160,89525,89892,90568,90832,91170,91523,91617,91803,92109,92371,92496,92623,92862,93073,93192,93385,93562,94017,94198,94320,94579,94692,94879,94981,95088,95217,95492,96000,96496,97373,97667,98237,98386,99118,99290,99374,99710,99802,100080,107099,112470,112532,113110,113694,113785,121754,121983,122143,122295,122466,122632,122801,122968,123131,123374,123544,123717,123888,124162,124361,124566,124896,124980,125076,125172,125270,125370,125472,125574,125676,125778,125880,125980,126076,126188,126317,126440,126571,126702,126800,126914,127008,127148,127282,127378,127490,127590,127706,127802,127914,128014,128154,128290,128454,128584,128742,128892,129033,129177,129312,129424,129574,129702,129830,129966,130098,130228,130358,130470,130610,131514,131658,131796,131862,131952,132028,132132,132222,132324,132432,132540,132640,132720,132812,132910,133020,133072,133150,133256,133348,133452,133562,133684,133847,134004,134494,134594,134684,134794,134884,135125,135219,135325,135417,135517,135629,135743,135859,135975,136069,136183,136295,136397,136517,136639,136721,136825,136945,137071,137169,137263,137351,137463,137579,137701,137813,137988,138104,138190,138282,138394,138518,138585,138711,138779,138907,139051,139179,139248,139343,139458,139571,139670,139779,139890,140001,140102,140207,140307,140437,140528,140651,140745,140857,140943,141047,141143,141231,141349,141453,141557,141683,141771,141879,141979,142069,142179,142263,142365,142449,142503,142567,142673,142759,142869,142953,143073,145973,146091,146206,146286,146647,146880,147750,148715,150059,151420,151808,154651,164704,165024,166998,168656,174684,179164,179426,179626,180652,184930,185536,186013,186164,190836,192714,193895,199009,200851,202982,203322,204633,204836"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/c6f094acc54b121e4168ffd043b0caa9/transformed/lifecycle-runtime-2.7.0/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "397", "startColumns": "4", "startOffsets": "22768", "endColumns": "42", "endOffsets": "22806"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/6589d333780c4589838d099a13cdb896/transformed/core-1.13.1/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "61,102,103,122,123,156,157,262,263,264,265,266,267,268,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,362,363,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,409,438,439,440,441,442,443,444,486,2030,2031,2035,2036,2040,2192,2193,2858,2903,3073,3106,3136,3169", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2034,3764,3836,5234,5299,7490,7559,14689,14759,14827,14899,14969,15030,15104,16494,16555,16616,16678,16742,16804,16865,16933,17033,17093,17159,17232,17301,17358,17410,18358,18430,18506,18571,18630,18689,18749,18809,18869,18929,18989,19049,19109,19169,19229,19289,19348,19408,19468,19528,19588,19648,19708,19768,19828,19888,19948,20007,20067,20127,20186,20245,20304,20363,20422,20990,21025,21611,21666,21729,21784,21842,21900,21961,22024,22081,22132,22182,22243,22300,22366,22400,22435,23476,25495,25562,25634,25703,25772,25846,25918,30037,130615,130732,130933,131043,131244,143078,143150,165029,167003,174838,176569,177569,178251", "endLines": "61,102,103,122,123,156,157,262,263,264,265,266,267,268,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,362,363,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,409,438,439,440,441,442,443,444,486,2030,2034,2035,2039,2040,2192,2193,2863,2912,3105,3126,3168,3174", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "2089,3831,3919,5294,5360,7554,7617,14754,14822,14894,14964,15025,15099,15172,16550,16611,16673,16737,16799,16860,16928,17028,17088,17154,17227,17296,17353,17405,17467,18425,18501,18566,18625,18684,18744,18804,18864,18924,18984,19044,19104,19164,19224,19284,19343,19403,19463,19523,19583,19643,19703,19763,19823,19883,19943,20002,20062,20122,20181,20240,20299,20358,20417,20476,21020,21055,21661,21724,21779,21837,21895,21956,22019,22076,22127,22177,22238,22295,22361,22395,22430,22465,23541,25557,25629,25698,25767,25841,25913,26001,30103,130727,130928,131038,131239,131368,143145,143212,165227,167299,176564,177245,178246,178413"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/697d4a371d14a343f07c7af23d58e667/transformed/jetified-window-1.2.0/res/values/values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "34,37,43,51,62,74,80,86,87,88,89,90,356,2301,2307,3662,3670,3685", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1154,1329,1502,1721,2094,2408,2596,2783,2836,2896,2948,2993,20686,146885,147080,193900,194182,194796", "endLines": "34,42,50,58,73,79,85,86,87,88,89,90,356,2306,2311,3669,3684,3700", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "1208,1497,1716,1935,2403,2591,2778,2831,2891,2943,2988,3027,20741,147075,147233,194177,194791,195445"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/db5aee305bfe9518f24fb54a44c845c5/transformed/jetified-appcompat-resources-1.6.1/res/values/values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2322,2338,2344,3701,3717", "startColumns": "4,4,4,4,4", "startOffsets": "147755,148180,148358,195450,195861", "endLines": "2337,2343,2353,3716,3720", "endColumns": "24,24,24,24,24", "endOffsets": "148175,148353,148637,195856,195983"}}, {"source": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/res/values/colors.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "56", "endOffsets": "107"}, "to": {"startLines": "143", "startColumns": "4", "startOffsets": "6761", "endColumns": "56", "endOffsets": "6813"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/c9d9b59e0faf7f1ee31dcea41f6a6a66/transformed/jetified-play-services-base-18.3.0/res/values/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "124,125,126,127,128,129,130,131,445,446,447,448,449,450,451,452,454,455,456,457,458,459,460,461,462,3221,3631", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5365,5455,5535,5625,5715,5795,5876,5956,26006,26111,26292,26417,26524,26704,26827,26943,27213,27401,27506,27687,27812,27987,28135,28198,28260,179963,193171", "endLines": "124,125,126,127,128,129,130,131,445,446,447,448,449,450,451,452,454,455,456,457,458,459,460,461,462,3233,3649", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "5450,5530,5620,5710,5790,5871,5951,6031,26106,26287,26412,26519,26699,26822,26938,27041,27396,27501,27682,27807,27982,28130,28193,28255,28334,180273,193583"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/a72323379a6772dd54f13756670be371/transformed/jetified-activity-1.8.1/res/values/values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "370,398", "startColumns": "4,4", "startOffsets": "21356,22811", "endColumns": "41,59", "endOffsets": "21393,22866"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/d3f77bc21a265068c68cb14dfa8ec314/transformed/lifecycle-viewmodel-2.7.0/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "400", "startColumns": "4", "startOffsets": "22925", "endColumns": "49", "endOffsets": "22970"}}, {"source": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/local_auth_android/intermediates/packaged_res/debug/packageDebugResources/values/values.xml", "from": {"startLines": "2,3,4,5,6", "startColumns": "4,4,4,4,4", "startOffsets": "55,100,144,190,238", "endLines": "2,3,4,5,11", "endColumns": "44,43,45,47,10", "endOffsets": "95,139,185,233,533"}, "to": {"startLines": "109,140,282,286,492", "startColumns": "4,4,4,4,4", "startOffsets": "4299,6582,16177,16446,30513", "endLines": "109,140,282,286,497", "endColumns": "44,43,45,47,10", "endOffsets": "4339,6621,16218,16489,30808"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/fbc8b9aaac8fe4f1ef014065c4488600/transformed/transition-1.4.1/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,95,142,185,240,287,341,393,442,503", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "90,137,180,235,282,336,388,437,498,548"}, "to": {"startLines": "358,359,364,371,372,391,392,393,394,395", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "20803,20843,21060,21398,21453,22470,22524,22576,22625,22686", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "20838,20885,21098,21448,21495,22519,22571,22620,22681,22731"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/1bcae24384bdfeff7289d35aaccaf47c/transformed/biometric-1.1.0/res/values/values.xml", "from": {"startLines": "2,6,8,11,15,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,194,277,388,523,1701,1757,1810,1886,1946,2035,2134,2242,2339,2427,2527,2597,2694,2804", "endLines": "5,7,10,14,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "endColumns": "19,19,19,19,19,55,52,75,59,88,98,107,96,87,99,69,96,109,88", "endOffsets": "189,272,383,518,1696,1752,1805,1881,1941,2030,2129,2237,2334,2422,2522,2592,2689,2799,2888"}, "to": {"startLines": "2,6,8,11,15,108,274,463,466,471,472,473,474,475,476,477,478,479,480", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,244,327,438,573,4243,15513,28339,28521,28853,28942,29041,29149,29246,29334,29434,29504,29601,29711", "endLines": "5,7,10,14,33,108,274,463,466,471,472,473,474,475,476,477,478,479,480", "endColumns": "19,19,19,19,19,55,52,75,59,88,98,107,96,87,99,69,96,109,88", "endOffsets": "239,322,433,568,1149,4294,15561,28410,28576,28937,29036,29144,29241,29329,29429,29499,29596,29706,29795"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/c0dfc2e5d5f124c9ed5c4a17b0deea03/transformed/preference-1.2.1/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,22,23,24,25,42,45,51,57,60,66,70,73,80,86,89,95,100,105,112,114,120,126,134,139,146,151,157,161,168,172,178,184,187,192,193,194,199,215,238,243,257,268,348,358,368,386,392,439,461,485", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,178,247,311,366,434,501,566,623,680,728,776,837,900,963,1001,1058,1102,1242,1381,1431,1479,2917,3022,3378,3716,3862,4202,4414,4577,4984,5322,5445,5784,6023,6280,6651,6711,7049,7335,7784,8076,8464,8769,9113,9358,9688,9895,10163,10436,10580,10949,10996,11052,11308,12367,13788,14126,15012,15622,20168,20687,21229,22503,22763,25467,26989,28470", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,21,22,23,24,41,44,50,56,59,65,69,72,79,85,88,94,99,104,111,113,119,125,133,138,145,150,156,160,167,171,177,183,186,191,192,193,198,214,237,242,256,267,347,357,367,385,391,438,460,484,508", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "173,242,306,361,429,496,561,618,675,723,771,832,895,958,996,1053,1097,1237,1376,1426,1474,2912,3017,3373,3711,3857,4197,4409,4572,4979,5317,5440,5779,6018,6275,6646,6706,7044,7330,7779,8071,8459,8764,9108,9353,9683,9890,10158,10431,10575,10944,10991,11047,11303,12362,13783,14121,15007,15617,20163,20682,21224,22498,22758,25462,26984,28465,29984"}, "to": {"startLines": "94,158,302,303,304,305,306,307,308,365,366,367,407,408,464,467,481,482,487,488,489,1584,1768,1771,1777,1783,1786,1792,1796,1799,1806,1812,1815,1821,1826,1831,1838,1840,1846,1852,1860,1865,1872,1877,1883,1887,1894,1898,1904,1910,1913,1917,1918,2849,2864,3031,3069,3211,3399,3417,3481,3491,3501,3508,3514,3618,3787,3804", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3190,7622,17472,17536,17591,17659,17726,17791,17848,21103,21151,21199,23350,23413,28415,28581,29800,29844,30108,30247,30297,100085,113790,113895,114140,114478,114624,114964,115176,115339,115746,116084,116207,116546,116785,117042,117413,117473,117811,118097,118546,118838,119226,119531,119875,120120,120450,120657,120925,121198,121342,121543,121590,164709,165232,173388,174689,179631,185541,186169,188094,188376,188681,188943,189203,192719,199014,199544", "endLines": "94,158,302,303,304,305,306,307,308,365,366,367,407,408,464,467,481,484,487,488,489,1600,1770,1776,1782,1785,1791,1795,1798,1805,1811,1814,1820,1825,1830,1837,1839,1845,1851,1859,1864,1871,1876,1882,1886,1893,1897,1903,1909,1912,1916,1917,1918,2853,2874,3050,3072,3220,3406,3480,3490,3500,3507,3513,3556,3630,3803,3820", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "3258,7686,17531,17586,17654,17721,17786,17843,17900,21146,21194,21255,23408,23471,28448,28633,29839,29979,30242,30292,30340,101518,113890,114135,114473,114619,114959,115171,115334,115741,116079,116202,116541,116780,117037,117408,117468,117806,118092,118541,118833,119221,119526,119870,120115,120445,120652,120920,121193,121337,121538,121585,121641,164889,165628,174112,174833,179958,185784,188089,188371,188676,188938,189198,190621,193166,199539,200107"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/29b3303d1a90bcdf02ffda76d7bde6d6/transformed/recyclerview-1.0.0/res/values/values.xml", "from": {"startLines": "30,31,32,33,34,35,36,2", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1535,1594,1642,1698,1773,1849,1921,55", "endLines": "30,31,32,33,34,35,36,29", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "1589,1637,1693,1768,1844,1916,1982,1530"}, "to": {"startLines": "271,272,273,283,284,285,361,3563", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "15350,15409,15457,16223,16298,16374,20924,190841", "endLines": "271,272,273,283,284,285,361,3582", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "15404,15452,15508,16293,16369,16441,20985,191631"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/6245e2c6672c9b1f6d6685b00614a41e/transformed/jetified-savedstate-1.2.1/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "399", "startColumns": "4", "startOffsets": "22871", "endColumns": "53", "endOffsets": "22920"}}, {"source": "/Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/res/values/styles.xml", "from": {"startLines": "3,14", "startColumns": "4,4", "startOffsets": "173,818", "endLines": "7,16", "endColumns": "12,12", "endOffsets": "476,982"}, "to": {"startLines": "1601,1605", "startColumns": "4,4", "startOffsets": "101523,101704", "endLines": "1604,1607", "endColumns": "12,12", "endOffsets": "101699,101868"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/27da46d50e2d40e533249fccc0ec02b9/transformed/jetified-core-common-2.0.3/res/values/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "166", "endLines": "11", "endColumns": "8", "endOffsets": "571"}, "to": {"startLines": "2077", "startColumns": "4", "startOffsets": "134009", "endLines": "2084", "endColumns": "8", "endOffsets": "134414"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/e706fccdb001d08d4a702269b55f3274/transformed/fragment-1.7.1/res/values/values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "357,373,401,3127,3132", "startColumns": "4,4,4,4,4", "startOffsets": "20746,21500,22975,177250,177420", "endLines": "357,373,401,3131,3135", "endColumns": "56,64,63,24,24", "endOffsets": "20798,21560,23034,177415,177564"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/e86131fafb6c5b403d511276b35b0d06/transformed/jetified-startup-runtime-1.1.1/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "437", "startColumns": "4", "startOffsets": "25412", "endColumns": "82", "endOffsets": "25490"}}]}]}