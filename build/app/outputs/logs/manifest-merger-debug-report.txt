-- Merging decision tree log ---
application
INJECTED from /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/AndroidManifest.xml:9:5-51:19
INJECTED from /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/debug/AndroidManifest.xml
MERGED from [:url_launcher_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-12:19
MERGED from [:url_launcher_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-12:19
MERGED from [:open_file_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/open_file_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-20:19
MERGED from [:open_file_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/open_file_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-20:19
MERGED from [com.google.android.play:app-update-ktx:2.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/af5e8d829900ed7834d438a231b711ae/transformed/jetified-app-update-ktx-2.1.0/AndroidManifest.xml:11:5-12:19
MERGED from [com.google.android.play:app-update-ktx:2.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/af5e8d829900ed7834d438a231b711ae/transformed/jetified-app-update-ktx-2.1.0/AndroidManifest.xml:11:5-12:19
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/697d4a371d14a343f07c7af23d58e667/transformed/jetified-window-1.2.0/AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/697d4a371d14a343f07c7af23d58e667/transformed/jetified-window-1.2.0/AndroidManifest.xml:22:5-29:19
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6b3eecdb85677d1cf7cfc939b614c019/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6b3eecdb85677d1cf7cfc939b614c019/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.play:app-update:2.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4f4fb959f91785e0eace7dfd278df616/transformed/jetified-app-update-2.1.0/AndroidManifest.xml:11:5-12:19
MERGED from [com.google.android.play:app-update:2.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4f4fb959f91785e0eace7dfd278df616/transformed/jetified-app-update-2.1.0/AndroidManifest.xml:11:5-12:19
MERGED from [com.google.android.gms:play-services-auth:21.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b221ec0a1202bc0a23dfdf8c6907b5a5/transformed/jetified-play-services-auth-21.2.0/AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:21.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b221ec0a1202bc0a23dfdf8c6907b5a5/transformed/jetified-play-services-auth-21.2.0/AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/0cc184ae81af6d294c63be2eb2434f3e/transformed/jetified-play-services-fido-20.0.1/AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/0cc184ae81af6d294c63be2eb2434f3e/transformed/jetified-play-services-fido-20.0.1/AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/c9d9b59e0faf7f1ee31dcea41f6a6a66/transformed/jetified-play-services-base-18.3.0/AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/c9d9b59e0faf7f1ee31dcea41f6a6a66/transformed/jetified-play-services-base-18.3.0/AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/691c4803ae34588ab86b053ed68e51d4/transformed/jetified-play-services-tasks-18.1.0/AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/691c4803ae34588ab86b053ed68e51d4/transformed/jetified-play-services-tasks-18.1.0/AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/74c58a9fa9a78d6cda2a74380ccc34d2/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/74c58a9fa9a78d6cda2a74380ccc34d2/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:20:5-24:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/3528b7108514549d665af8216f1a14d0/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/3528b7108514549d665af8216f1a14d0/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/6589d333780c4589838d099a13cdb896/transformed/core-1.13.1/AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/6589d333780c4589838d099a13cdb896/transformed/core-1.13.1/AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5aebf1ccadd4f90aa31b994548591d8/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5aebf1ccadd4f90aa31b994548591d8/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e86131fafb6c5b403d511276b35b0d06/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e86131fafb6c5b403d511276b35b0d06/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/3dd940b524076d9c7db4756d5ae3488f/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/3dd940b524076d9c7db4756d5ae3488f/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [com.google.android.play:core-common:2.0.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/27da46d50e2d40e533249fccc0ec02b9/transformed/jetified-core-common-2.0.3/AndroidManifest.xml:11:5-19:19
MERGED from [com.google.android.play:core-common:2.0.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/27da46d50e2d40e533249fccc0ec02b9/transformed/jetified-core-common-2.0.3/AndroidManifest.xml:11:5-19:19
	android:extractNativeLibs
		INJECTED from /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/debug/AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/6589d333780c4589838d099a13cdb896/transformed/core-1.13.1/AndroidManifest.xml:28:18-86
	android:name
		INJECTED from /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/AndroidManifest.xml
provider#androidx.core.content.FileProvider
INJECTED from /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/AndroidManifest.xml:13:9-24:20
	android:authorities
		INJECTED from /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/AndroidManifest.xml
manifest
ADDED from /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/AndroidManifest.xml:1:1-68:12
MERGED from /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/AndroidManifest.xml:1:1-68:12
INJECTED from /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/debug/AndroidManifest.xml:1:1-7:12
INJECTED from /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/debug/AndroidManifest.xml:1:1-7:12
INJECTED from /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/debug/AndroidManifest.xml:1:1-7:12
MERGED from [:device_info_plus] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/device_info_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:shared_preferences_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/shared_preferences_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:local_auth_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/local_auth_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-9:12
MERGED from [:sms_autofill] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/sms_autofill/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:url_launcher_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-14:12
MERGED from [:connectivity_plus] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/connectivity_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-9:12
MERGED from [:flutter_plugin_android_lifecycle] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/flutter_plugin_android_lifecycle/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:flutter_secure_storage] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/flutter_secure_storage/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-10:12
MERGED from [:in_app_update] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/in_app_update/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:open_file_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/open_file_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-22:12
MERGED from [:path_provider_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/path_provider_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:permission_handler_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/permission_handler_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:vibration] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/vibration/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.play:app-update-ktx:2.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/af5e8d829900ed7834d438a231b711ae/transformed/jetified-app-update-ktx-2.1.0/AndroidManifest.xml:2:1-14:12
MERGED from [androidx.biometric:biometric:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1bcae24384bdfeff7289d35aaccaf47c/transformed/biometric-1.1.0/AndroidManifest.xml:17:1-29:12
MERGED from [androidx.preference:preference:1.2.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/c0dfc2e5d5f124c9ed5c4a17b0deea03/transformed/preference-1.2.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/db5aee305bfe9518f24fb54a44c845c5/transformed/jetified-appcompat-resources-1.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/71d91ec26cc715ac194e9af65b3a48ac/transformed/appcompat-1.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.browser:browser:1.8.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ff9de53921e81b72effb184a4ee977a5/transformed/browser-1.8.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/29b3303d1a90bcdf02ffda76d7bde6d6/transformed/recyclerview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/79fa3712d034b7b27a27167c7a69ba8a/transformed/legacy-support-core-ui-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/83501d488c54b3b695f983dc6997aeca/transformed/slidingpanelayout-1.2.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/697d4a371d14a343f07c7af23d58e667/transformed/jetified-window-1.2.0/AndroidManifest.xml:17:1-31:12
MERGED from [androidx.window:window-java:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/92c869cb5c7c198ece31cadf9e83a5e4/transformed/jetified-window-java-1.2.0/AndroidManifest.xml:17:1-21:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/29ec9b0c23980438ee7baa1dfbf5f016/transformed/drawerlayout-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cb0380ab80c0d8779eff2bef92878692/transformed/jetified-emoji2-views-helper-1.2.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6b3eecdb85677d1cf7cfc939b614c019/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:17:1-35:12
MERGED from [com.google.android.play:app-update:2.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4f4fb959f91785e0eace7dfd278df616/transformed/jetified-app-update-2.1.0/AndroidManifest.xml:2:1-14:12
MERGED from [com.google.android.gms:play-services-auth:21.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b221ec0a1202bc0a23dfdf8c6907b5a5/transformed/jetified-play-services-auth-21.2.0/AndroidManifest.xml:17:1-40:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/d14e372ae08e9aa37b1119edb4f5fef4/transformed/jetified-play-services-auth-api-phone-18.0.2/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] /Users/<USER>/.gradle/caches/8.10.2/transforms/39cc7e266de9af159baf7c9f825e3b4f/transformed/jetified-play-services-auth-base-18.0.10/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-fido:20.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/0cc184ae81af6d294c63be2eb2434f3e/transformed/jetified-play-services-fido-20.0.1/AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/c9d9b59e0faf7f1ee31dcea41f6a6a66/transformed/jetified-play-services-base-18.3.0/AndroidManifest.xml:16:1-24:12
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/691c4803ae34588ab86b053ed68e51d4/transformed/jetified-play-services-tasks-18.1.0/AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/74c58a9fa9a78d6cda2a74380ccc34d2/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment-ktx:1.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/52d8e7e8797758fd5aa39efdf036c37f/transformed/jetified-fragment-ktx-1.7.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e706fccdb001d08d4a702269b55f3274/transformed/fragment-1.7.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/3c890b28935e043f102329bc23dbbe96/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/25a6d93aaea939448fc1be84b753341b/transformed/loader-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity-ktx:1.8.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a32263c91e26cd0ae564575978421c58/transformed/jetified-activity-ktx-1.8.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a72323379a6772dd54f13756670be371/transformed/jetified-activity-1.8.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/65c17f70a707c40e20af987044441bf3/transformed/viewpager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/15fdb7643237388b2968ff5e0255b42c/transformed/jetified-lifecycle-runtime-ktx-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/7692a644986bde55937c42c972318964/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/058207b44934de3aed0cca5f804dea7a/transformed/jetified-lifecycle-viewmodel-ktx-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3f77bc21a265068c68cb14dfa8ec314/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/30003cb3f150f52b0e10a4a176224c7b/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/c6f094acc54b121e4168ffd043b0caa9/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/3528b7108514549d665af8216f1a14d0/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/09e8ee65b86eee20eaca713b643b7887/transformed/jetified-savedstate-ktx-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/6245e2c6672c9b1f6d6685b00614a41e/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9b0e4fe8b58531002bdf711a390802d5/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/558c11d71905424945f5f96af59411e9/transformed/jetified-core-ktx-1.13.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e1191a39f53db8b97b15efcc2e8372ca/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/dccb2f1e2983a8a4f7fd43a123f4a93d/transformed/vectordrawable-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/50ac106c41e657d661b010ef25e09e3e/transformed/coordinatorlayout-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d55b71cfa27953b69c9b9fad7e71def7/transformed/customview-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/fbc8b9aaac8fe4f1ef014065c4488600/transformed/transition-1.4.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/2644cf7ff40ea9d8fc0354ef4411b134/transformed/swiperefreshlayout-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/256db71c5b72c83a7bc9d895df96802b/transformed/asynclayoutinflater-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/6589d333780c4589838d099a13cdb896/transformed/core-1.13.1/AndroidManifest.xml:17:1-30:12
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] /Users/<USER>/.gradle/caches/8.10.2/transforms/bf8bf1c8b68d6449133f24bcac9c8844/transformed/jetified-security-crypto-1.1.0-alpha06/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5aebf1ccadd4f90aa31b994548591d8/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e86131fafb6c5b403d511276b35b0d06/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/f00f75c06477d03ef94c393ac4309f7c/transformed/jetified-tracing-1.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/3dd940b524076d9c7db4756d5ae3488f/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:17:1-27:12
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/854b973e91aaee717d0dba00f99d5cdb/transformed/interpolator-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/25ead4f1832549e3db89d2693b0da140/transformed/cursoradapter-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-core-android:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/4a79ee9b581052149fb0405a099f25a5/transformed/jetified-datastore-core-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/7286148f6784cd2142f396d38d90dc4d/transformed/jetified-datastore-preferences-release/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-android:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/4f4da9c4232d4df3cdca24d2de461349/transformed/jetified-datastore-release/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a26d0d4ba1c5012752de1d3543bf8987/transformed/core-runtime-2.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/194bc6b61f578d1664c15d27e2d9e233/transformed/jetified-core-1.0.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b2e53fb0b33f01e6c17967b873a85917/transformed/documentfile-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/975f6259939122ef24bb972146631b71/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9d0d44da3ac00cab8d8eb8ecc4f855cd/transformed/print-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9a3b0fd25d81aa9166e2389b98239956/transformed/jetified-annotation-experimental-1.4.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] /Users/<USER>/.gradle/caches/8.10.2/transforms/72288ed492184e0f9250d24de8966306/transformed/jetified-relinker-1.4.5/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.play:core-common:2.0.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/27da46d50e2d40e533249fccc0ec02b9/transformed/jetified-core-common-2.0.3/AndroidManifest.xml:2:1-21:12
	package
		INJECTED from /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/debug/AndroidManifest.xml
	android:versionName
		INJECTED from /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/debug/AndroidManifest.xml
	xmlns:tools
		ADDED from /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/AndroidManifest.xml:1:11-57
		ADDED from /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/AndroidManifest.xml:1:11-57
	android:versionCode
		INJECTED from /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/debug/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/AndroidManifest.xml:2:5-63
uses-permission#android.permission.VIBRATE
ADDED from /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/AndroidManifest.xml:3:5-66
MERGED from [:vibration] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/vibration/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-66
MERGED from [:vibration] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/vibration/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-66
	android:name
		ADDED from /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/AndroidManifest.xml:3:22-63
uses-permission#android.permission.INTERNET
ADDED from /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/AndroidManifest.xml:4:5-67
MERGED from /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/AndroidManifest.xml:4:5-67
MERGED from /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/AndroidManifest.xml:4:5-67
	android:name
		ADDED from /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/AndroidManifest.xml:4:22-64
uses-permission#android.permission.USE_BIOMETRIC
ADDED from /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/AndroidManifest.xml:5:5-71
MERGED from [:local_auth_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/local_auth_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-72
MERGED from [:local_auth_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/local_auth_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-72
MERGED from [androidx.biometric:biometric:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1bcae24384bdfeff7289d35aaccaf47c/transformed/biometric-1.1.0/AndroidManifest.xml:24:5-72
MERGED from [androidx.biometric:biometric:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1bcae24384bdfeff7289d35aaccaf47c/transformed/biometric-1.1.0/AndroidManifest.xml:24:5-72
	android:name
		ADDED from /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/AndroidManifest.xml:5:22-69
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/AndroidManifest.xml:6:5-80
	android:name
		ADDED from /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/AndroidManifest.xml:6:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/AndroidManifest.xml:7:5-79
	android:name
		ADDED from /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/AndroidManifest.xml:7:22-77
queries
ADDED from /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/AndroidManifest.xml:57:5-66:15
intent#action:name:android.intent.action.PROCESS_TEXT+data:mimeType:text/plain
ADDED from /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/AndroidManifest.xml:58:9-61:18
action#android.intent.action.PROCESS_TEXT
ADDED from /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/AndroidManifest.xml:59:13-72
	android:name
		ADDED from /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/AndroidManifest.xml:59:21-70
data
ADDED from /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/AndroidManifest.xml:60:13-50
	android:mimeType
		ADDED from /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/AndroidManifest.xml:60:19-48
intent#action:name:android.intent.action.VIEW+data:scheme:otpauth
ADDED from /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/AndroidManifest.xml:62:9-65:18
action#android.intent.action.VIEW
ADDED from /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/AndroidManifest.xml:63:13-65
	android:name
		ADDED from /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/main/AndroidManifest.xml:63:21-62
uses-sdk
INJECTED from /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/debug/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/debug/AndroidManifest.xml
INJECTED from /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/debug/AndroidManifest.xml
MERGED from [:device_info_plus] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/device_info_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:device_info_plus] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/device_info_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/shared_preferences_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/shared_preferences_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:local_auth_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/local_auth_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:local_auth_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/local_auth_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:sms_autofill] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/sms_autofill/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:sms_autofill] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/sms_autofill/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:connectivity_plus] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/connectivity_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:connectivity_plus] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/connectivity_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/flutter_plugin_android_lifecycle/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/flutter_plugin_android_lifecycle/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:flutter_secure_storage] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/flutter_secure_storage/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:6:5-8:53
MERGED from [:flutter_secure_storage] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/flutter_secure_storage/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:6:5-8:53
MERGED from [:in_app_update] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/in_app_update/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:in_app_update] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/in_app_update/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:open_file_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/open_file_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:6:5-44
MERGED from [:open_file_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/open_file_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:6:5-44
MERGED from [:path_provider_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/path_provider_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/path_provider_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/permission_handler_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/permission_handler_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:vibration] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/vibration/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:vibration] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/vibration/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:app-update-ktx:2.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/af5e8d829900ed7834d438a231b711ae/transformed/jetified-app-update-ktx-2.1.0/AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:app-update-ktx:2.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/af5e8d829900ed7834d438a231b711ae/transformed/jetified-app-update-ktx-2.1.0/AndroidManifest.xml:7:5-9:41
MERGED from [androidx.biometric:biometric:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1bcae24384bdfeff7289d35aaccaf47c/transformed/biometric-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.biometric:biometric:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1bcae24384bdfeff7289d35aaccaf47c/transformed/biometric-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/c0dfc2e5d5f124c9ed5c4a17b0deea03/transformed/preference-1.2.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/c0dfc2e5d5f124c9ed5c4a17b0deea03/transformed/preference-1.2.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/db5aee305bfe9518f24fb54a44c845c5/transformed/jetified-appcompat-resources-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/db5aee305bfe9518f24fb54a44c845c5/transformed/jetified-appcompat-resources-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/71d91ec26cc715ac194e9af65b3a48ac/transformed/appcompat-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/71d91ec26cc715ac194e9af65b3a48ac/transformed/appcompat-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.browser:browser:1.8.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ff9de53921e81b72effb184a4ee977a5/transformed/browser-1.8.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ff9de53921e81b72effb184a4ee977a5/transformed/browser-1.8.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/29b3303d1a90bcdf02ffda76d7bde6d6/transformed/recyclerview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/29b3303d1a90bcdf02ffda76d7bde6d6/transformed/recyclerview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/79fa3712d034b7b27a27167c7a69ba8a/transformed/legacy-support-core-ui-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/79fa3712d034b7b27a27167c7a69ba8a/transformed/legacy-support-core-ui-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/83501d488c54b3b695f983dc6997aeca/transformed/slidingpanelayout-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/83501d488c54b3b695f983dc6997aeca/transformed/slidingpanelayout-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/697d4a371d14a343f07c7af23d58e667/transformed/jetified-window-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/697d4a371d14a343f07c7af23d58e667/transformed/jetified-window-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/92c869cb5c7c198ece31cadf9e83a5e4/transformed/jetified-window-java-1.2.0/AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/92c869cb5c7c198ece31cadf9e83a5e4/transformed/jetified-window-java-1.2.0/AndroidManifest.xml:19:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/29ec9b0c23980438ee7baa1dfbf5f016/transformed/drawerlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/29ec9b0c23980438ee7baa1dfbf5f016/transformed/drawerlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cb0380ab80c0d8779eff2bef92878692/transformed/jetified-emoji2-views-helper-1.2.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/cb0380ab80c0d8779eff2bef92878692/transformed/jetified-emoji2-views-helper-1.2.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6b3eecdb85677d1cf7cfc939b614c019/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6b3eecdb85677d1cf7cfc939b614c019/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:21:5-44
MERGED from [com.google.android.play:app-update:2.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4f4fb959f91785e0eace7dfd278df616/transformed/jetified-app-update-2.1.0/AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:app-update:2.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4f4fb959f91785e0eace7dfd278df616/transformed/jetified-app-update-2.1.0/AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.gms:play-services-auth:21.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b221ec0a1202bc0a23dfdf8c6907b5a5/transformed/jetified-play-services-auth-21.2.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:21.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b221ec0a1202bc0a23dfdf8c6907b5a5/transformed/jetified-play-services-auth-21.2.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/d14e372ae08e9aa37b1119edb4f5fef4/transformed/jetified-play-services-auth-api-phone-18.0.2/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/d14e372ae08e9aa37b1119edb4f5fef4/transformed/jetified-play-services-auth-api-phone-18.0.2/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] /Users/<USER>/.gradle/caches/8.10.2/transforms/39cc7e266de9af159baf7c9f825e3b4f/transformed/jetified-play-services-auth-base-18.0.10/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] /Users/<USER>/.gradle/caches/8.10.2/transforms/39cc7e266de9af159baf7c9f825e3b4f/transformed/jetified-play-services-auth-base-18.0.10/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/0cc184ae81af6d294c63be2eb2434f3e/transformed/jetified-play-services-fido-20.0.1/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/0cc184ae81af6d294c63be2eb2434f3e/transformed/jetified-play-services-fido-20.0.1/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/c9d9b59e0faf7f1ee31dcea41f6a6a66/transformed/jetified-play-services-base-18.3.0/AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/c9d9b59e0faf7f1ee31dcea41f6a6a66/transformed/jetified-play-services-base-18.3.0/AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/691c4803ae34588ab86b053ed68e51d4/transformed/jetified-play-services-tasks-18.1.0/AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/691c4803ae34588ab86b053ed68e51d4/transformed/jetified-play-services-tasks-18.1.0/AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/74c58a9fa9a78d6cda2a74380ccc34d2/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/74c58a9fa9a78d6cda2a74380ccc34d2/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment-ktx:1.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/52d8e7e8797758fd5aa39efdf036c37f/transformed/jetified-fragment-ktx-1.7.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/52d8e7e8797758fd5aa39efdf036c37f/transformed/jetified-fragment-ktx-1.7.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e706fccdb001d08d4a702269b55f3274/transformed/fragment-1.7.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e706fccdb001d08d4a702269b55f3274/transformed/fragment-1.7.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/3c890b28935e043f102329bc23dbbe96/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/3c890b28935e043f102329bc23dbbe96/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/25a6d93aaea939448fc1be84b753341b/transformed/loader-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/25a6d93aaea939448fc1be84b753341b/transformed/loader-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity-ktx:1.8.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a32263c91e26cd0ae564575978421c58/transformed/jetified-activity-ktx-1.8.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a32263c91e26cd0ae564575978421c58/transformed/jetified-activity-ktx-1.8.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a72323379a6772dd54f13756670be371/transformed/jetified-activity-1.8.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a72323379a6772dd54f13756670be371/transformed/jetified-activity-1.8.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/65c17f70a707c40e20af987044441bf3/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/65c17f70a707c40e20af987044441bf3/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/15fdb7643237388b2968ff5e0255b42c/transformed/jetified-lifecycle-runtime-ktx-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/15fdb7643237388b2968ff5e0255b42c/transformed/jetified-lifecycle-runtime-ktx-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/7692a644986bde55937c42c972318964/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/7692a644986bde55937c42c972318964/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/058207b44934de3aed0cca5f804dea7a/transformed/jetified-lifecycle-viewmodel-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/058207b44934de3aed0cca5f804dea7a/transformed/jetified-lifecycle-viewmodel-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3f77bc21a265068c68cb14dfa8ec314/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d3f77bc21a265068c68cb14dfa8ec314/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/30003cb3f150f52b0e10a4a176224c7b/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/30003cb3f150f52b0e10a4a176224c7b/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/c6f094acc54b121e4168ffd043b0caa9/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/c6f094acc54b121e4168ffd043b0caa9/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/3528b7108514549d665af8216f1a14d0/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/3528b7108514549d665af8216f1a14d0/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/09e8ee65b86eee20eaca713b643b7887/transformed/jetified-savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/09e8ee65b86eee20eaca713b643b7887/transformed/jetified-savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/6245e2c6672c9b1f6d6685b00614a41e/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/6245e2c6672c9b1f6d6685b00614a41e/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9b0e4fe8b58531002bdf711a390802d5/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9b0e4fe8b58531002bdf711a390802d5/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/558c11d71905424945f5f96af59411e9/transformed/jetified-core-ktx-1.13.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/558c11d71905424945f5f96af59411e9/transformed/jetified-core-ktx-1.13.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e1191a39f53db8b97b15efcc2e8372ca/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e1191a39f53db8b97b15efcc2e8372ca/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/dccb2f1e2983a8a4f7fd43a123f4a93d/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/dccb2f1e2983a8a4f7fd43a123f4a93d/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/50ac106c41e657d661b010ef25e09e3e/transformed/coordinatorlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/50ac106c41e657d661b010ef25e09e3e/transformed/coordinatorlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d55b71cfa27953b69c9b9fad7e71def7/transformed/customview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d55b71cfa27953b69c9b9fad7e71def7/transformed/customview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/fbc8b9aaac8fe4f1ef014065c4488600/transformed/transition-1.4.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/fbc8b9aaac8fe4f1ef014065c4488600/transformed/transition-1.4.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/2644cf7ff40ea9d8fc0354ef4411b134/transformed/swiperefreshlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/2644cf7ff40ea9d8fc0354ef4411b134/transformed/swiperefreshlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/256db71c5b72c83a7bc9d895df96802b/transformed/asynclayoutinflater-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/256db71c5b72c83a7bc9d895df96802b/transformed/asynclayoutinflater-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/6589d333780c4589838d099a13cdb896/transformed/core-1.13.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/6589d333780c4589838d099a13cdb896/transformed/core-1.13.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] /Users/<USER>/.gradle/caches/8.10.2/transforms/bf8bf1c8b68d6449133f24bcac9c8844/transformed/jetified-security-crypto-1.1.0-alpha06/AndroidManifest.xml:20:5-44
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] /Users/<USER>/.gradle/caches/8.10.2/transforms/bf8bf1c8b68d6449133f24bcac9c8844/transformed/jetified-security-crypto-1.1.0-alpha06/AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5aebf1ccadd4f90aa31b994548591d8/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5aebf1ccadd4f90aa31b994548591d8/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e86131fafb6c5b403d511276b35b0d06/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e86131fafb6c5b403d511276b35b0d06/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/f00f75c06477d03ef94c393ac4309f7c/transformed/jetified-tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/f00f75c06477d03ef94c393ac4309f7c/transformed/jetified-tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/3dd940b524076d9c7db4756d5ae3488f/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/3dd940b524076d9c7db4756d5ae3488f/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/854b973e91aaee717d0dba00f99d5cdb/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/854b973e91aaee717d0dba00f99d5cdb/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/25ead4f1832549e3db89d2693b0da140/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/25ead4f1832549e3db89d2693b0da140/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/4a79ee9b581052149fb0405a099f25a5/transformed/jetified-datastore-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/4a79ee9b581052149fb0405a099f25a5/transformed/jetified-datastore-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/7286148f6784cd2142f396d38d90dc4d/transformed/jetified-datastore-preferences-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/7286148f6784cd2142f396d38d90dc4d/transformed/jetified-datastore-preferences-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/4f4da9c4232d4df3cdca24d2de461349/transformed/jetified-datastore-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/4f4da9c4232d4df3cdca24d2de461349/transformed/jetified-datastore-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a26d0d4ba1c5012752de1d3543bf8987/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a26d0d4ba1c5012752de1d3543bf8987/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/194bc6b61f578d1664c15d27e2d9e233/transformed/jetified-core-1.0.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/194bc6b61f578d1664c15d27e2d9e233/transformed/jetified-core-1.0.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b2e53fb0b33f01e6c17967b873a85917/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b2e53fb0b33f01e6c17967b873a85917/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/975f6259939122ef24bb972146631b71/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/975f6259939122ef24bb972146631b71/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9d0d44da3ac00cab8d8eb8ecc4f855cd/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9d0d44da3ac00cab8d8eb8ecc4f855cd/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9a3b0fd25d81aa9166e2389b98239956/transformed/jetified-annotation-experimental-1.4.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9a3b0fd25d81aa9166e2389b98239956/transformed/jetified-annotation-experimental-1.4.0/AndroidManifest.xml:5:5-44
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] /Users/<USER>/.gradle/caches/8.10.2/transforms/72288ed492184e0f9250d24de8966306/transformed/jetified-relinker-1.4.5/AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] /Users/<USER>/.gradle/caches/8.10.2/transforms/72288ed492184e0f9250d24de8966306/transformed/jetified-relinker-1.4.5/AndroidManifest.xml:5:5-43
MERGED from [com.google.android.play:core-common:2.0.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/27da46d50e2d40e533249fccc0ec02b9/transformed/jetified-core-common-2.0.3/AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:core-common:2.0.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/27da46d50e2d40e533249fccc0ec02b9/transformed/jetified-core-common-2.0.3/AndroidManifest.xml:7:5-9:41
	tools:overrideLibrary
		ADDED from [:flutter_secure_storage] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/flutter_secure_storage/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-50
	android:targetSdkVersion
		INJECTED from /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/debug/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/android/app/src/debug/AndroidManifest.xml
activity#io.flutter.plugins.urllauncher.WebViewActivity
ADDED from [:url_launcher_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-11:74
	android:exported
		ADDED from [:url_launcher_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [:url_launcher_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-71
	android:name
		ADDED from [:url_launcher_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-74
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [:connectivity_plus] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/connectivity_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-79
	android:name
		ADDED from [:connectivity_plus] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/connectivity_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:22-76
provider#com.crazecoder.openfile.FileProvider
ADDED from [:open_file_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/open_file_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:9-19:20
	android:requestLegacyExternalStorage
		ADDED from [:open_file_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/open_file_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:13-56
	android:grantUriPermissions
		ADDED from [:open_file_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/open_file_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:13-47
	android:authorities
		ADDED from [:open_file_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/open_file_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-88
	android:exported
		ADDED from [:open_file_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/open_file_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:13-37
	tools:replace
		ADDED from [:open_file_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/open_file_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:13-48
	android:name
		ADDED from [:open_file_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/open_file_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-64
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:open_file_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/open_file_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:16:13-18:53
	android:resource
		ADDED from [:open_file_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/open_file_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:18:17-50
	android:name
		ADDED from [:open_file_android] /Users/<USER>/Downloads/Nudron/Nudron-Flutter-Water-Meter/build/open_file_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:17:17-67
uses-permission#android.permission.USE_FINGERPRINT
ADDED from [androidx.biometric:biometric:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1bcae24384bdfeff7289d35aaccaf47c/transformed/biometric-1.1.0/AndroidManifest.xml:27:5-74
	android:name
		ADDED from [androidx.biometric:biometric:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1bcae24384bdfeff7289d35aaccaf47c/transformed/biometric-1.1.0/AndroidManifest.xml:27:22-71
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/697d4a371d14a343f07c7af23d58e667/transformed/jetified-window-1.2.0/AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/697d4a371d14a343f07c7af23d58e667/transformed/jetified-window-1.2.0/AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/697d4a371d14a343f07c7af23d58e667/transformed/jetified-window-1.2.0/AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/697d4a371d14a343f07c7af23d58e667/transformed/jetified-window-1.2.0/AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/697d4a371d14a343f07c7af23d58e667/transformed/jetified-window-1.2.0/AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/697d4a371d14a343f07c7af23d58e667/transformed/jetified-window-1.2.0/AndroidManifest.xml:27:13-51
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6b3eecdb85677d1cf7cfc939b614c019/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/3528b7108514549d665af8216f1a14d0/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/3528b7108514549d665af8216f1a14d0/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5aebf1ccadd4f90aa31b994548591d8/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5aebf1ccadd4f90aa31b994548591d8/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e86131fafb6c5b403d511276b35b0d06/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e86131fafb6c5b403d511276b35b0d06/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6b3eecdb85677d1cf7cfc939b614c019/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6b3eecdb85677d1cf7cfc939b614c019/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6b3eecdb85677d1cf7cfc939b614c019/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6b3eecdb85677d1cf7cfc939b614c019/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6b3eecdb85677d1cf7cfc939b614c019/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6b3eecdb85677d1cf7cfc939b614c019/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6b3eecdb85677d1cf7cfc939b614c019/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:30:17-75
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:21.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b221ec0a1202bc0a23dfdf8c6907b5a5/transformed/jetified-play-services-auth-21.2.0/AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:21.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b221ec0a1202bc0a23dfdf8c6907b5a5/transformed/jetified-play-services-auth-21.2.0/AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b221ec0a1202bc0a23dfdf8c6907b5a5/transformed/jetified-play-services-auth-21.2.0/AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:21.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b221ec0a1202bc0a23dfdf8c6907b5a5/transformed/jetified-play-services-auth-21.2.0/AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b221ec0a1202bc0a23dfdf8c6907b5a5/transformed/jetified-play-services-auth-21.2.0/AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:21.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b221ec0a1202bc0a23dfdf8c6907b5a5/transformed/jetified-play-services-auth-21.2.0/AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b221ec0a1202bc0a23dfdf8c6907b5a5/transformed/jetified-play-services-auth-21.2.0/AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:21.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b221ec0a1202bc0a23dfdf8c6907b5a5/transformed/jetified-play-services-auth-21.2.0/AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:21.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b221ec0a1202bc0a23dfdf8c6907b5a5/transformed/jetified-play-services-auth-21.2.0/AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b221ec0a1202bc0a23dfdf8c6907b5a5/transformed/jetified-play-services-auth-21.2.0/AndroidManifest.xml:34:13-89
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/c9d9b59e0faf7f1ee31dcea41f6a6a66/transformed/jetified-play-services-base-18.3.0/AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/c9d9b59e0faf7f1ee31dcea41f6a6a66/transformed/jetified-play-services-base-18.3.0/AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/c9d9b59e0faf7f1ee31dcea41f6a6a66/transformed/jetified-play-services-base-18.3.0/AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/c9d9b59e0faf7f1ee31dcea41f6a6a66/transformed/jetified-play-services-base-18.3.0/AndroidManifest.xml:20:19-85
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/74c58a9fa9a78d6cda2a74380ccc34d2/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/74c58a9fa9a78d6cda2a74380ccc34d2/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/74c58a9fa9a78d6cda2a74380ccc34d2/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:22:13-58
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/3528b7108514549d665af8216f1a14d0/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/3528b7108514549d665af8216f1a14d0/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/3528b7108514549d665af8216f1a14d0/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/6589d333780c4589838d099a13cdb896/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/6589d333780c4589838d099a13cdb896/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/6589d333780c4589838d099a13cdb896/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
permission#com.nudron.water_meter2.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/6589d333780c4589838d099a13cdb896/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/6589d333780c4589838d099a13cdb896/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/6589d333780c4589838d099a13cdb896/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/6589d333780c4589838d099a13cdb896/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/6589d333780c4589838d099a13cdb896/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
uses-permission#com.nudron.water_meter2.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/6589d333780c4589838d099a13cdb896/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/6589d333780c4589838d099a13cdb896/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5aebf1ccadd4f90aa31b994548591d8/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5aebf1ccadd4f90aa31b994548591d8/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5aebf1ccadd4f90aa31b994548591d8/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5aebf1ccadd4f90aa31b994548591d8/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5aebf1ccadd4f90aa31b994548591d8/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5aebf1ccadd4f90aa31b994548591d8/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5aebf1ccadd4f90aa31b994548591d8/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5aebf1ccadd4f90aa31b994548591d8/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5aebf1ccadd4f90aa31b994548591d8/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5aebf1ccadd4f90aa31b994548591d8/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5aebf1ccadd4f90aa31b994548591d8/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5aebf1ccadd4f90aa31b994548591d8/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5aebf1ccadd4f90aa31b994548591d8/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5aebf1ccadd4f90aa31b994548591d8/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5aebf1ccadd4f90aa31b994548591d8/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5aebf1ccadd4f90aa31b994548591d8/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5aebf1ccadd4f90aa31b994548591d8/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5aebf1ccadd4f90aa31b994548591d8/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5aebf1ccadd4f90aa31b994548591d8/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5aebf1ccadd4f90aa31b994548591d8/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/a5aebf1ccadd4f90aa31b994548591d8/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
activity#com.google.android.play.core.common.PlayCoreDialogWrapperActivity
ADDED from [com.google.android.play:core-common:2.0.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/27da46d50e2d40e533249fccc0ec02b9/transformed/jetified-core-common-2.0.3/AndroidManifest.xml:14:9-18:65
	android:stateNotNeeded
		ADDED from [com.google.android.play:core-common:2.0.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/27da46d50e2d40e533249fccc0ec02b9/transformed/jetified-core-common-2.0.3/AndroidManifest.xml:17:13-42
	android:exported
		ADDED from [com.google.android.play:core-common:2.0.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/27da46d50e2d40e533249fccc0ec02b9/transformed/jetified-core-common-2.0.3/AndroidManifest.xml:16:13-37
	android:theme
		ADDED from [com.google.android.play:core-common:2.0.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/27da46d50e2d40e533249fccc0ec02b9/transformed/jetified-core-common-2.0.3/AndroidManifest.xml:18:13-62
	android:name
		ADDED from [com.google.android.play:core-common:2.0.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/27da46d50e2d40e533249fccc0ec02b9/transformed/jetified-core-common-2.0.3/AndroidManifest.xml:15:13-93
