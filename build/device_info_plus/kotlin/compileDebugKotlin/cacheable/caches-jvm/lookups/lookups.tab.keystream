  ActivityManager android.app  isLowRamDevice android.app.ActivityManager  Context android.content  ACTIVITY_SERVICE android.content.Context  getSystemService android.content.Context  packageManager android.content.Context  FeatureInfo android.content.pm  PackageManager android.content.pm  name android.content.pm.FeatureInfo  systemAvailableFeatures !android.content.pm.PackageManager  Build 
android.os  BOARD android.os.Build  
BOOTLOADER android.os.Build  BRAND android.os.Build  DEVICE android.os.Build  DISPLAY android.os.Build  FINGERPRINT android.os.Build  HARDWARE android.os.Build  HOST android.os.Build  ID android.os.Build  MANUFACTURER android.os.Build  MODEL android.os.Build  PRODUCT android.os.Build  SERIAL android.os.Build  SUPPORTED_32_BIT_ABIS android.os.Build  SUPPORTED_64_BIT_ABIS android.os.Build  SUPPORTED_ABIS android.os.Build  TAGS android.os.Build  TYPE android.os.Build  UNKNOWN android.os.Build  	getSerial android.os.Build  BASE_OS android.os.Build.VERSION  CODENAME android.os.Build.VERSION  INCREMENTAL android.os.Build.VERSION  PREVIEW_SDK_INT android.os.Build.VERSION  RELEASE android.os.Build.VERSION  SDK_INT android.os.Build.VERSION  SECURITY_PATCH android.os.Build.VERSION  LOLLIPOP android.os.Build.VERSION_CODES  M android.os.Build.VERSION_CODES  O android.os.Build.VERSION_CODES  DisplayMetrics android.util  Display android.view  
WindowManager android.view  ActivityManager %dev.fluttercommunity.plus.device_info  Any %dev.fluttercommunity.plus.device_info  Array %dev.fluttercommunity.plus.device_info  BinaryMessenger %dev.fluttercommunity.plus.device_info  Boolean %dev.fluttercommunity.plus.device_info  Build %dev.fluttercommunity.plus.device_info  Context %dev.fluttercommunity.plus.device_info  DeviceInfoPlusPlugin %dev.fluttercommunity.plus.device_info  FeatureInfo %dev.fluttercommunity.plus.device_info  
FlutterPlugin %dev.fluttercommunity.plus.device_info  HashMap %dev.fluttercommunity.plus.device_info  List %dev.fluttercommunity.plus.device_info  
MethodCall %dev.fluttercommunity.plus.device_info  MethodCallHandler %dev.fluttercommunity.plus.device_info  MethodCallHandlerImpl %dev.fluttercommunity.plus.device_info  
MethodChannel %dev.fluttercommunity.plus.device_info  
MutableMap %dev.fluttercommunity.plus.device_info  PackageManager %dev.fluttercommunity.plus.device_info  SecurityException %dev.fluttercommunity.plus.device_info  String %dev.fluttercommunity.plus.device_info  contains %dev.fluttercommunity.plus.device_info  	emptyList %dev.fluttercommunity.plus.device_info  	filterNot %dev.fluttercommunity.plus.device_info  listOf %dev.fluttercommunity.plus.device_info  map %dev.fluttercommunity.plus.device_info  set %dev.fluttercommunity.plus.device_info  
startsWith %dev.fluttercommunity.plus.device_info  Context :dev.fluttercommunity.plus.device_info.DeviceInfoPlusPlugin  MethodCallHandlerImpl :dev.fluttercommunity.plus.device_info.DeviceInfoPlusPlugin  
MethodChannel :dev.fluttercommunity.plus.device_info.DeviceInfoPlusPlugin  
methodChannel :dev.fluttercommunity.plus.device_info.DeviceInfoPlusPlugin  setupMethodChannel :dev.fluttercommunity.plus.device_info.DeviceInfoPlusPlugin  FlutterPluginBinding 3dev.fluttercommunity.plus.device_info.FlutterPlugin  Build ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  HashMap ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  activityManager ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  contains ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  	emptyList ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  	filterNot ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  getSystemFeatures ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  
isEmulator ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  listOf ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  map ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  packageManager ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  set ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  
startsWith ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  Result 3dev.fluttercommunity.plus.device_info.MethodChannel  
FlutterPlugin #io.flutter.embedding.engine.plugins  FlutterPluginBinding 1io.flutter.embedding.engine.plugins.FlutterPlugin  applicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  binaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  BinaryMessenger io.flutter.plugin.common  
MethodCall io.flutter.plugin.common  
MethodChannel io.flutter.plugin.common  method #io.flutter.plugin.common.MethodCall  MethodCallHandler &io.flutter.plugin.common.MethodChannel  Result &io.flutter.plugin.common.MethodChannel  setMethodCallHandler &io.flutter.plugin.common.MethodChannel  notImplemented -io.flutter.plugin.common.MethodChannel.Result  success -io.flutter.plugin.common.MethodChannel.Result  SecurityException 	java.lang  HashMap 	java.util  Array kotlin  CharSequence kotlin  	Function1 kotlin  Result kotlin  map kotlin  	filterNot kotlin.Array  not kotlin.Boolean  	compareTo 
kotlin.Int  equals 
kotlin.String  HashMap kotlin.collections  List kotlin.collections  Map kotlin.collections  
MutableMap kotlin.collections  contains kotlin.collections  	emptyList kotlin.collections  	filterNot kotlin.collections  listOf kotlin.collections  map kotlin.collections  set kotlin.collections  map kotlin.collections.List  set kotlin.collections.MutableMap  
startsWith 	kotlin.io  contains 
kotlin.ranges  Sequence kotlin.sequences  contains kotlin.sequences  	filterNot kotlin.sequences  map kotlin.sequences  contains kotlin.text  	filterNot kotlin.text  map kotlin.text  set kotlin.text  
startsWith kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         